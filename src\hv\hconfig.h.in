#ifndef HV_CONFIG_H_
#define HV_CONFIG_H_

#ifndef HAVE_STDBOOL_H
#define HAVE_STDBOOL_H @HAVE_STDBOOL_H@
#endif

#ifndef HAVE_STDINT_H
#define HAVE_STDINT_H @HAVE_STDINT_H@
#endif

#ifndef HAVE_STDATOMIC_H
#define HAVE_STDATOMIC_H @HAVE_STDATOMIC_H@
#endif

#ifndef HAVE_SYS_TYPES_H
#define HAVE_SYS_TYPES_H @HAVE_SYS_TYPES_H@
#endif

#ifndef HAVE_SYS_STAT_H
#define HAVE_SYS_STAT_H @HAVE_SYS_STAT_H@
#endif

#ifndef HAVE_SYS_TIME_H
#define HAVE_SYS_TIME_H @HAVE_SYS_TIME_H@
#endif

#ifndef HAVE_FCNTL_H
#define HAVE_FCNTL_H @HAVE_FCNTL_H@
#endif

#ifndef HAVE_PTHREAD_H
#define HAVE_PTHREAD_H @HAVE_PTHREAD_H@
#endif

#ifndef HAVE_GETTID
#define HAVE_GETTID @HAVE_GETTID@
#endif

#ifndef HAVE_STRLCPY
#define HAVE_STRLCPY @HAVE_STRLCPY@
#endif

#ifndef HAVE_STRLCAT
#define HAVE_STRLCAT @HAVE_STRLCAT@
#endif

#ifndef HAVE_CLOCK_GETTIME
#define HAVE_CLOCK_GETTIME @HAVE_CLOCK_GETTIME@
#endif

#ifndef HAVE_GETTIMEOFDAY
#define HAVE_GETTIMEOFDAY @HAVE_GETTIMEOFDAY@
#endif

#ifndef HAVE_PTHREAD_SPIN_LOCK
#define HAVE_PTHREAD_SPIN_LOCK @HAVE_PTHREAD_SPIN_LOCK@
#endif

#ifndef HAVE_PTHREAD_MUTEX_TIMEDLOCK
#define HAVE_PTHREAD_MUTEX_TIMEDLOCK @HAVE_PTHREAD_MUTEX_TIMEDLOCK@
#endif

#ifndef HAVE_SEM_TIMEDWAIT
#define HAVE_SEM_TIMEDWAIT @HAVE_SEM_TIMEDWAIT@
#endif

#cmakedefine ENABLE_UDS     1
#cmakedefine USE_MULTIMAP   1

#endif // HV_CONFIG_H_
