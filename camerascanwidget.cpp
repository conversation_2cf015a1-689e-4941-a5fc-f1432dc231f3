#include "camerascanwidget.h"
#include "configmanager.h"
#include "logmanager.h"
#include <QDebug>
#include <QMessageBox>
#include <QIcon>
#include <QTimer>
#include <QAction>
#include <QHeaderView>
#include <QCheckBox>
#include <QPushButton>
#include <QResizeEvent>
#include <QBrush>
#include <QColor>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QMenu>
#include <QInputDialog>
#include <QLineEdit>
#include <QMutex>
#include <QMutexLocker>
#include <QFont>
#include <QEvent>

// 添加一个静态互斥锁以同步操作
static QMutex operationMutex;

CameraScanWidget::CameraScanWidget(QWidget *parent)
    : QWidget(parent), m_scanner(new CameraScanner(this)), m_isScanning(false)
{
    // 设置背景图片，改为铺满而不是拉伸
    setStyleSheet("CameraScanWidget { background-image: url(:/images/list_bg.png); background-position: center; background-repeat: repeat; background-color: #001F3F; }");

    initUI();

    // 安装事件过滤器
    m_cameraTableWidget->installEventFilter(this);

    // 连接扫描器信号
    connect(m_scanner, &CameraScanner::scanProgress, this, &CameraScanWidget::onScanProgress);
    connect(m_scanner, &CameraScanner::cameraFound, this, &CameraScanWidget::onCameraFound);
    connect(m_scanner, &CameraScanner::scanCompleted, this, &CameraScanWidget::onScanCompleted);

    // 使用定时器延迟启动，确保界面已经完全加载
    // 使用直接连接方式，避免线程安全问题
    QTimer *initTimer = new QTimer(this);
    initTimer->setSingleShot(true);
    connect(initTimer, &QTimer::timeout, this, [this, initTimer]()
            {
        // 检查是否是首次扫描
        if (ConfigManager::instance().isFirstScan()) {
            // 首次扫描，启动扫描过程
            startScanning();
        } else {
            // 非首次扫描，直接从数据库加载
            loadCamerasFromDatabase();
        }
        // 删除定时器
        initTimer->deleteLater(); });
    initTimer->start(500);
}

CameraScanWidget::~CameraScanWidget()
{
    if (m_scanner->isRunning())
    {
        m_scanner->stopScan();
        m_scanner->wait();
    }
}

QString CameraScanWidget::getSelectedRtspUrl() const
{
    QModelIndexList selectedRows = m_cameraTableWidget->selectionModel()->selectedRows();
    if (!selectedRows.isEmpty())
    {
        int row = selectedRows.first().row();
        // RTSP URL存储在表格的用户数据中
        return m_cameraTableWidget->item(row, NameColumn)->data(Qt::UserRole).toString();
    }
    return QString();
}

void CameraScanWidget::initUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0); // 去掉边距，与监控画面对齐
    m_mainLayout->setSpacing(0);

    // 添加标题标签
    QLabel *titleLabel = new QLabel("摄像头配置", this);
    titleLabel->setStyleSheet("QLabel { color: #FFFFFF; background-color: #003366; padding: 5px; font-weight: bold; font-size: 14px; }");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setFixedHeight(30); // 固定高度确保与右侧标题对齐

    m_mainLayout->addWidget(titleLabel);

    // 创建进度条和重新扫描按钮的水平布局
    QHBoxLayout *progressLayout = new QHBoxLayout();
    progressLayout->setContentsMargins(9, 5, 9, 5); // 左右保持9像素边距，上下保持5像素边距
    progressLayout->setSpacing(10);                 // 增加组件之间的间距

    // 进度条
    // m_progressBar = new QProgressBar(this);
    // m_progressBar->setRange(0, 100);
    m_statusLabel = new QLabel("准备扫描...", this);
    m_statusLabel->setStyleSheet("QLabel { color: #00FFFF; font-weight: bold; padding: 3px; font-size: 12px; }"); // 改为青色加粗字体，提高可见度
    m_statusLabel->setContentsMargins(0, 0, 0, 0);
    // m_progressBar->setAlignment(Qt::AlignCenter); // 数字居中显示
    // m_progressBar->setStyleSheet("QProgressBar { border: 1px solid #00FFFF; border-radius: 3px; background-color: #003366; height: 20px; color: #FFFFFF; font-weight: bold; font-size: 12px; } "
    //                              "QProgressBar::chunk { background-color: #00FFFF; }");

    // 重新扫描按钮
    m_rescanButton = new QPushButton("重新扫描", this);
    m_rescanButton->setToolTip("重新扫描网络中的摄像头");
    m_rescanButton->setFixedWidth(85); // 略微增加宽度
    m_rescanButton->setStyleSheet("QPushButton { background-color: #003366; color: #FFFFFF; border: 1px solid #00FFFF; border-radius: 3px; padding: 4px; font-size: 12px; font-weight: bold; } "
                                  "QPushButton:hover { background-color: #004080; } "
                                  "QPushButton:pressed { background-color: #002040; }");

    // 添加到水平布局
    progressLayout->addWidget(m_statusLabel, 1); // 状态标签占据剩余空间
    progressLayout->addWidget(m_rescanButton);

    // 添加所有组件到主布局
    m_mainLayout->addLayout(progressLayout); // 添加水平布局

    // 摄像头表格
    m_cameraTableWidget = new QTableWidget(this);
    m_cameraTableWidget->setColumnCount(4);
    m_cameraTableWidget->setHorizontalHeaderLabels(QStringList() << "名称" << "IP地址" << "状态" << "操作");
    m_cameraTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_cameraTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_cameraTableWidget->setEditTriggers(QAbstractItemView::DoubleClicked | QAbstractItemView::EditKeyPressed | QAbstractItemView::SelectedClicked);
    m_cameraTableWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    m_cameraTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff); // 隐藏水平滚动条
    m_cameraTableWidget->setAlternatingRowColors(false);                       // 禁用交替行颜色
    m_cameraTableWidget->setShowGrid(false);                                   // 隐藏网格线，使界面更现代
    m_cameraTableWidget->setStyleSheet("QTableWidget { border: none; background-color: rgba(0, 30, 60, 150); } "
                                       "QHeaderView::section { background-color: #003366; color: #00FFFF; border: none; padding: 4px; font-weight: bold; font-size: 13px; } "
                                       "QTableWidget::item { border-bottom: 1px solid rgba(0, 255, 255, 40); padding: 2px; color: white; font-size: 12px; background-color: transparent; }"
                                       "QTableWidget::item:selected { background-color: rgba(0, 128, 255, 120); color: white; }"
                                       "QTableWidget::item:focus { outline: none; }"
                                       "QLineEdit { background-color: #001F3F; color: white; selection-background-color: #0078D7; selection-color: white; }");

    // 修改表格列宽设置，确保名称能够完全显示，IP地址列自动拉伸
    m_cameraTableWidget->horizontalHeader()->setSectionResizeMode(NameColumn, QHeaderView::Interactive);
    m_cameraTableWidget->horizontalHeader()->setSectionResizeMode(IpColumn, QHeaderView::Stretch);
    m_cameraTableWidget->horizontalHeader()->setSectionResizeMode(StatusColumn, QHeaderView::Interactive);
    m_cameraTableWidget->horizontalHeader()->setSectionResizeMode(OperationColumn, QHeaderView::Fixed);

    // 设置列的初始宽度
    m_cameraTableWidget->setColumnWidth(NameColumn, 120);     // 名称列宽度
    m_cameraTableWidget->setColumnWidth(StatusColumn, 70);    // 状态列宽度
    m_cameraTableWidget->setColumnWidth(OperationColumn, 35); // 操作列宽度

    // 设置初始IP列宽度与名称列相同，但它会自动拉伸
    m_cameraTableWidget->setColumnWidth(IpColumn, 130);

    m_cameraTableWidget->verticalHeader()->setVisible(false);
    m_cameraTableWidget->verticalHeader()->setDefaultSectionSize(35); // 减小默认行高为35像素

    // 创建右键菜单
    m_contextMenu = new QMenu(this);
    QAction *rescanAction = new QAction("重新扫描", this);
    m_contextMenu->addAction(rescanAction);

    // 创建表格容器，添加边距
    QWidget *tableContainer = new QWidget(this);
    // 设置表格容器背景为透明
    tableContainer->setStyleSheet("QWidget { background-color: transparent; }");
    QHBoxLayout *tableLayout = new QHBoxLayout(tableContainer);
    tableLayout->setContentsMargins(9, 5, 9, 9); // 调整边距，顶部增加一点空间
    tableLayout->addWidget(m_cameraTableWidget);

    m_mainLayout->addWidget(tableContainer, 1); // 表格容器占据剩余空间

    // 连接信号和槽
    connect(m_cameraTableWidget, &QTableWidget::itemSelectionChanged,
            this, &CameraScanWidget::onCameraListSelectionChanged);
    connect(m_cameraTableWidget, &QTableWidget::customContextMenuRequested,
            this, &CameraScanWidget::showContextMenu);
    connect(rescanAction, &QAction::triggered, this, &CameraScanWidget::rescan);
    connect(m_rescanButton, &QPushButton::clicked, this, &CameraScanWidget::rescan);
    connect(m_cameraTableWidget, &QTableWidget::itemChanged,
            this, &CameraScanWidget::onItemChanged);
}

void CameraScanWidget::startScanning()
{
    LOG_DEBUG("Starting scanning process");

    // 如果已经在扫描，先停止
    if (m_scanner->isRunning())
    {
        LOG_DEBUG("Scanner is already running, stopping it first");
        m_scanner->stopScan();
        m_scanner->wait();
    }

    // 获取当前配置的IP段
    QString ipPrefix = ConfigManager::instance().getIpPrefix();
    int startIp = ConfigManager::instance().getStartIp();
    int endIp = ConfigManager::instance().getEndIp();

    // 记录需要保留的摄像头行
    QList<int> rowsToKeep;
    QList<QString> ipsInScanRange;

    // 生成当前扫描范围内的所有IP地址
    for (int i = startIp; i <= endIp; i++)
    {
        ipsInScanRange.append(QString("%1.%2").arg(ipPrefix).arg(i));
    }

    LOG_DEBUG("Scanning IP range: " << ipPrefix << "." << startIp << " to " << ipPrefix << "." << endIp);

    // 查找在扫描范围内的摄像头行
    for (int row = 0; row < m_cameraTableWidget->rowCount(); row++)
    {
        QString ipAddress = m_cameraTableWidget->item(row, IpColumn)->text();
        if (ipsInScanRange.contains(ipAddress))
        {
            // 该IP在扫描范围内，需要移除
            LOG_DEBUG("IP " << ipAddress << " is in scan range, will be rescanned");
        }
        else
        {
            // 该IP不在扫描范围内，需要保留
            rowsToKeep.append(row);
            LOG_DEBUG("IP " << ipAddress << " is not in scan range, will be kept");
        }
    }

    // 清空活跃IP地址列表，但只保存当前扫描范围内的IP
    m_activeIpAddresses.clear();

    // 更新UI状态
    updateUIState(true);
    m_statusLabel->setText("正在扫描...");

    // 临时存储需要保留的摄像头信息
    QList<CameraInfo> camerasToKeep;
    for (int row : rowsToKeep)
    {
        CameraInfo camera;
        camera.name = m_cameraTableWidget->item(row, NameColumn)->text();
        camera.ipAddress = m_cameraTableWidget->item(row, IpColumn)->text();
        camera.rtspUrl = m_cameraTableWidget->item(row, NameColumn)->data(Qt::UserRole).toString();

        // 获取勾选状态
        QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(row, OperationColumn);
        if (checkboxWidget)
        {
            QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
            camera.isChecked = checkbox && checkbox->isChecked();
        }

        camerasToKeep.append(camera);
        LOG_DEBUG("Keeping camera: " << camera.rtspUrl << ", checked: " << camera.isChecked);
    }

    // 安全地清空表格
    LOG_DEBUG("Clearing table, current row count: " << m_cameraTableWidget->rowCount());
    m_cameraTableWidget->clearContents();
    m_cameraTableWidget->setRowCount(0);
    // 确保没有选中的行
    m_cameraTableWidget->clearSelection();
    LOG_DEBUG("Table cleared");

    // 重新添加保留的摄像头
    for (const CameraInfo &camera : camerasToKeep)
    {
        LOG_DEBUG("Re-adding camera to table: " << camera.rtspUrl);
        addCameraToTable(camera);
    }

    // 开始扫描
    LOG_DEBUG("Starting scan thread");
    m_scanner->startScan();
}

void CameraScanWidget::loadCamerasFromDatabase()
{
    // 从数据库加载摄像头信息
    QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();

    // 清空表格
    m_cameraTableWidget->setRowCount(0);

    // 确保没有选中项
    m_cameraTableWidget->clearSelection();

    // 更新状态
    m_statusLabel->setText("正在加载摄像头信息...");
    // m_progressBar->setValue(0);

    // 添加摄像头到表格
    for (const CameraInfo &camera : cameras)
    {
        addCameraToTable(camera);

        // 如果摄像头被勾选，则更新其状态为"在线"
        if (camera.isChecked)
        {
            // 找到刚添加的摄像头所在的行
            int row = m_cameraTableWidget->rowCount() - 1;
            updateCameraStatus(row, "在线");
        }
    }

    // 更新状态
    m_statusLabel->setText(QString("已加载 %1 个摄像头").arg(cameras.size()));
    // m_progressBar->setValue(100);

    // 再次确保没有选中项
    m_cameraTableWidget->clearSelection();

    // 遍历所有行，找出勾选的摄像头并发送信号
    QList<QString> selectedCameras;
    for (int row = 0; row < m_cameraTableWidget->rowCount(); row++)
    {
        QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(row, OperationColumn);
        if (checkboxWidget)
        {
            QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
            if (checkbox && checkbox->isChecked())
            {
                QString rtspUrl = m_cameraTableWidget->item(row, NameColumn)->data(Qt::UserRole).toString();
                selectedCameras.append(rtspUrl);
                LOG_DEBUG("加载已勾选摄像头: " << rtspUrl);
            }
        }
    }

    // 使用定时器延迟发送信号，确保UI已完全加载
    if (!selectedCameras.isEmpty())
    {
        QTimer *signalTimer = new QTimer(this);
        signalTimer->setSingleShot(true);
        connect(signalTimer, &QTimer::timeout, this, [this, selectedCameras, signalTimer]()
                {
                    for (const QString &rtspUrl : selectedCameras)
                    {
                        LOG_DEBUG("发送已勾选摄像头信号: " << rtspUrl);
                        emit cameraSelected(rtspUrl);
                    }
                    signalTimer->deleteLater(); });
        signalTimer->start(500); // 延迟500毫秒发送信号
    }
}

void CameraScanWidget::addCameraToTable(const CameraInfo &camera)
{
    int row = m_cameraTableWidget->rowCount();
    m_cameraTableWidget->insertRow(row);

    // 为所有单元格设置统一的背景色
    QBrush rowBackground(QColor(0, 30, 60, 150)); // 深蓝色半透明背景

    // 名称列 - 使用传入的名称，不再自行生成
    QString deviceName = camera.name;

    QTableWidgetItem *nameItem = new QTableWidgetItem(deviceName);
    nameItem->setIcon(QIcon(":/images/camera.png"));
    nameItem->setData(Qt::UserRole, camera.rtspUrl);              // 存储RTSP URL
    nameItem->setTextAlignment(Qt::AlignVCenter | Qt::AlignLeft); // 文本左对齐，垂直居中
    nameItem->setToolTip(deviceName);                             // 添加tooltip显示完整名称
    // 设置图标大小为16x16像素
    m_cameraTableWidget->setIconSize(QSize(16, 16));
    // 设置统一的背景色
    nameItem->setBackground(rowBackground);
    // 确保名称列是可编辑的
    nameItem->setFlags(nameItem->flags() | Qt::ItemIsEditable);
    m_cameraTableWidget->setItem(row, NameColumn, nameItem);

    // IP地址列 - 不允许编辑
    QTableWidgetItem *ipItem = new QTableWidgetItem(camera.ipAddress);
    ipItem->setTextAlignment(Qt::AlignVCenter | Qt::AlignCenter); // 文本居中对齐，垂直居中
    ipItem->setFlags(ipItem->flags() & ~Qt::ItemIsEditable);      // 禁止编辑
    ipItem->setToolTip(camera.ipAddress);                         // 添加tooltip显示完整IP地址
    // 设置统一的背景色
    ipItem->setBackground(rowBackground);
    m_cameraTableWidget->setItem(row, IpColumn, ipItem);

    // 状态列 - 默认离线，不允许编辑
    QTableWidgetItem *statusItem = new QTableWidgetItem("离线");
    statusItem->setTextAlignment(Qt::AlignVCenter | Qt::AlignCenter); // 文本居中
    statusItem->setFlags(statusItem->flags() & ~Qt::ItemIsEditable);  // 禁止编辑
    // 设置统一的背景色
    statusItem->setBackground(rowBackground);
    // 设置文本颜色为灰色
    statusItem->setForeground(QBrush(QColor("#808080")));
    m_cameraTableWidget->setItem(row, StatusColumn, statusItem);

    // 操作列 - 添加复选框
    QWidget *checkboxContainer = new QWidget();
    checkboxContainer->setStyleSheet("background-color: transparent;"); // 确保容器背景透明
    QHBoxLayout *layout = new QHBoxLayout(checkboxContainer);
    QCheckBox *checkbox = new QCheckBox();
    checkbox->setProperty("row", row);
    checkbox->setProperty("rtspUrl", camera.rtspUrl);

    // 移除复选框自定义样式，使用系统默认样式
    // checkbox->setStyleSheet("QCheckBox::indicator { width: 16px; height: 16px; }"
    //                       "QCheckBox::indicator:unchecked { border: 2px solid #808080; background-color: transparent; }"
    //                       "QCheckBox::indicator:checked { border: 2px solid #00FFFF; background-color: #00FFFF; }");

    // 设置居中
    layout->addWidget(checkbox);
    layout->setAlignment(Qt::AlignCenter);
    layout->setContentsMargins(0, 0, 0, 0);

    LOG_DEBUG("Adding checkbox for camera: " << camera.rtspUrl << " at row " << row);

    // 连接复选框的状态变化信号，先断开所有旧连接以防止重复连接
    checkbox->disconnect();
    connect(checkbox, &QCheckBox::stateChanged, this, &CameraScanWidget::onOperationButtonClicked);

    m_cameraTableWidget->setCellWidget(row, OperationColumn, checkboxContainer);

    // 直接设置勾选状态，不使用延迟计时器
    if (camera.isChecked)
    {
        LOG_DEBUG("Setting checkbox checked for camera: " << camera.rtspUrl);
        // 临时阻止信号，直接设置勾选状态
        checkbox->blockSignals(true);
        checkbox->setChecked(true);
        checkbox->blockSignals(false);

        // 如果摄像头被勾选，则将状态更新为"在线"
        updateCameraStatus(row, "在线");
    }
}

void CameraScanWidget::updateUIState(bool scanning)
{
    m_isScanning = scanning;

    // 扫描过程中将重新扫描按钮变为停止按钮
    if (scanning)
    {
        m_rescanButton->setText("停止扫描");
        m_rescanButton->setToolTip("停止当前扫描过程并显示已扫描结果");
    }
    else
    {
        m_rescanButton->setText("重新扫描");
        m_rescanButton->setToolTip("重新扫描网络中的摄像头");
    }

    // 按钮始终保持启用状态
    m_rescanButton->setEnabled(true);

    if (!scanning)
    {
        // m_progressBar->setValue(100); // 设置为100%，与从数据库加载保持一致
        m_statusLabel->setText("扫描完成");
    }
}

void CameraScanWidget::onScanProgress(int current, int total)
{
    int percent = (current * 100) / total;
    // m_progressBar->setValue(percent);
    m_statusLabel->setText(QString("扫描中，扫描进度: %1/%2").arg(current).arg(total));
}

void CameraScanWidget::onCameraFound(const CameraInfo &camera)
{
    // 记录活跃IP地址
    if (!m_activeIpAddresses.contains(camera.ipAddress))
    {
        m_activeIpAddresses.append(camera.ipAddress);
    }

    // 先检查数据库中是否已存在相同IP的摄像头
    CameraInfo existingCamera = ConfigManager::instance().findCameraByIp(camera.ipAddress);
    CameraInfo savedCamera = camera;

    if (!existingCamera.name.isEmpty())
    {
        // 如果已存在，使用已有的名称
        savedCamera.name = existingCamera.name;
        savedCamera.isChecked = existingCamera.isChecked; // 保持原来的勾选状态
        LOG_DEBUG("use exist : IP=" << camera.ipAddress << ", name=" << existingCamera.name
                                    << ", checked=" << existingCamera.isChecked);
    }

    // 保存到数据库
    ConfigManager::instance().saveCameraToDatabase(savedCamera);

    // 重新从数据库加载该摄像头，确保名称一致
    QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();
    for (const CameraInfo &loadedCamera : cameras)
    {
        if (loadedCamera.rtspUrl == camera.rtspUrl)
        {
            savedCamera = loadedCamera;
            break;
        }
    }

    // 添加到表格，使用数据库中的名称
    addCameraToTable(savedCamera);

    LOG_INFO("found camera:" << savedCamera.rtspUrl << ", name:" << savedCamera.name
                             << ", checked:" << savedCamera.isChecked);

    // 使用成员变量替代匿名计时器，避免线程安全问题
    QTimer *statusTimer = new QTimer(this);
    statusTimer->setSingleShot(true);
    connect(statusTimer, &QTimer::timeout, this, [this, savedCamera, statusTimer]()
            {
        int row = -1;
        // 查找对应行
        for (int i = 0; i < m_cameraTableWidget->rowCount(); i++) {
            if (m_cameraTableWidget->item(i, NameColumn)->data(Qt::UserRole).toString() == savedCamera.rtspUrl) {
                row = i;
                break;
            }
        }
        
        if (row >= 0) {
            updateCameraStatus(row, "在线");
            
            // 确保复选框的连接
            QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(row, OperationColumn);
            if (checkboxWidget) {
                // 设置复选框容器背景为透明
                checkboxWidget->setStyleSheet("background-color: transparent;");
                QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
                if (checkbox) {
                    LOG_DEBUG("Ensuring checkbox connection for camera: " << savedCamera.rtspUrl);
                    checkbox->disconnect();
                    connect(checkbox, &QCheckBox::stateChanged, this, &CameraScanWidget::onOperationButtonClicked);
                    checkbox->setProperty("row", row);
                    checkbox->setProperty("rtspUrl", savedCamera.rtspUrl);
                }
            }
        }
        
        // 删除计时器
        statusTimer->deleteLater(); });
    statusTimer->start(500); // 减少延迟时间
}

void CameraScanWidget::updateCameraStatus(int row, const QString &status)
{
    // 获取当前行的背景色，从名称列获取
    QBrush background = m_cameraTableWidget->item(row, NameColumn)->background();

    QTableWidgetItem *statusItem = new QTableWidgetItem(status);
    statusItem->setTextAlignment(Qt::AlignVCenter | Qt::AlignCenter); // 文本居中
    statusItem->setFlags(statusItem->flags() & ~Qt::ItemIsEditable);  // 禁止编辑
    // 保持行背景色统一
    statusItem->setBackground(background);

    // 根据状态设置不同的颜色和字体
    if (status == "在线")
    {
        statusItem->setForeground(QBrush(QColor("#00FFFF"))); // 在线状态使用青色
        QFont font = statusItem->font();
        font.setBold(true);
        statusItem->setFont(font);
    }
    else if (status == "离线")
    {
        statusItem->setForeground(QBrush(QColor("#808080"))); // 离线状态使用灰色
    }

    m_cameraTableWidget->setItem(row, StatusColumn, statusItem);
}

void CameraScanWidget::onScanCompleted()
{
    LOG_DEBUG("Scan completed, updating UI state");
    updateUIState(false);

    // 获取当前配置的IP段
    QString ipPrefix = ConfigManager::instance().getIpPrefix();
    int startIp = ConfigManager::instance().getStartIp();
    int endIp = ConfigManager::instance().getEndIp();

    // 生成当前扫描范围内的所有IP地址
    QList<QString> ipsInScanRange;
    for (int i = startIp; i <= endIp; i++)
    {
        ipsInScanRange.append(QString("%1.%2").arg(ipPrefix).arg(i));
    }

    // 过滤活跃IP地址列表，确保只删除扫描范围内的不活跃摄像头
    // 只有当非用户手动停止时才删除不活跃摄像头
    if (!m_activeIpAddresses.isEmpty() && !ipsInScanRange.isEmpty() && !m_scanner->isUserStopped())
    {
        LOG_DEBUG("Deleting inactive cameras in scan range");
        // 删除扫描范围内不在活跃列表中的摄像头
        bool success = ConfigManager::instance().deleteInactiveCameras(m_activeIpAddresses, ipsInScanRange);
        if (success)
        {
            LOG_INFO("delete inactive cameras in scan range success");
        }
        else
        {
            LOG_WARNING("delete inactive cameras in scan range failed");
        }
    }

    LOG_DEBUG("Reconnecting checkbox signals for " << m_cameraTableWidget->rowCount() << " cameras");
    // 确保所有复选框都连接了正确的信号
    for (int row = 0; row < m_cameraTableWidget->rowCount(); row++)
    {
        QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(row, OperationColumn);
        if (checkboxWidget)
        {
            QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
            if (checkbox)
            {
                // 断开所有旧的连接
                checkbox->disconnect();

                // 重新连接信号
                connect(checkbox, &QCheckBox::stateChanged, this, &CameraScanWidget::onOperationButtonClicked);

                // 确保设置了正确的row和rtspUrl属性
                checkbox->setProperty("row", row);
                QString rtspUrl = m_cameraTableWidget->item(row, NameColumn)->data(Qt::UserRole).toString();
                checkbox->setProperty("rtspUrl", rtspUrl);

                LOG_DEBUG("Reconnected checkbox at row " << row << " for camera " << rtspUrl);
            }
            else
            {
                LOG_WARNING("No checkbox found at row " << row);
            }
        }
        else
        {
            LOG_WARNING("No checkbox widget found at row " << row);
        }
    }

    // 重新启用按钮和交互
    m_blockInteraction = false;
    LOG_DEBUG("Buttons and table re-enabled");

    // 确保没有行被选中
    m_cameraTableWidget->clearSelection();

    // 计算找到的摄像头数量（在当前扫描范围内的）
    int foundCount = 0;
    for (int row = 0; row < m_cameraTableWidget->rowCount(); row++)
    {
        QString ipAddress = m_cameraTableWidget->item(row, IpColumn)->text();
        if (ipsInScanRange.contains(ipAddress))
        {
            foundCount++;
        }
    }

    // 根据扫描状态设置不同的消息
    if (m_scanner->isUserStopped())
    {
        m_statusLabel->setText(QString("扫描已停止，已找到 %1 个摄像头").arg(foundCount));
    }
    else
    {
        m_statusLabel->setText(QString("扫描完成，已找到 %1 个摄像头").arg(foundCount));
    }

    // 如果是正常完成的扫描，并且没有找到摄像头，显示提示框
    if (foundCount == 0 && !m_scanner->isUserStopped())
    {
        QMessageBox msgBox;
        msgBox.setWindowTitle("扫描结果");
        msgBox.setFixedSize(280, 150);

        // 设置样式表，与截图提示框保持一致
        msgBox.setStyleSheet(
            "QMessageBox {"
            "  background-color: white;"
            "}"
            "QLabel {"
            "  color: #333333;"
            "  font-size: 12px;"
            "}"
            "QPushButton {"
            "  background-color: #0078D7;"
            "  color: white;"
            "  border: none;"
            "  padding: 6px 16px;"
            "  border-radius: 3px;"
            "  min-width: 80px;"
            "}"
            "QPushButton:hover {"
            "  background-color: #1683D8;"
            "}");

        msgBox.setText("扫描完成");
        msgBox.setInformativeText("未找到可用摄像头");
        msgBox.setIcon(QMessageBox::Information);
        msgBox.setStandardButtons(QMessageBox::Ok);
        msgBox.button(QMessageBox::Ok)->setText("确定");

        msgBox.exec();
    }
    else
    {
        // 标记首次扫描已完成
        ConfigManager::instance().setFirstScanCompleted();
    }
}

void CameraScanWidget::onCameraListSelectionChanged()
{
    // 只记录选择变化，不自动显示摄像头
}

void CameraScanWidget::showContextMenu(const QPoint &pos)
{
    if (m_contextMenu)
    {
        m_contextMenu->exec(m_cameraTableWidget->mapToGlobal(pos));
    }
}

void CameraScanWidget::rescan()
{
    // 如果当前在扫描中，则停止扫描
    if (m_isScanning)
    {
        LOG_DEBUG("Stopping ongoing scan operation");

        // 停止扫描线程
        if (m_scanner->isRunning())
        {
            m_scanner->stopScan();
            // 等待扫描线程退出，最多等待1秒
            if (!m_scanner->wait(1000))
            {
                LOG_WARNING("Scanner thread did not exit in time, forcing termination");
                m_scanner->terminate();
                m_scanner->wait();
            }
        }

        // 更新UI状态，恢复为重新扫描按钮
        updateUIState(false);

        // 设置状态信息
        m_statusLabel->setText("扫描已停止，显示当前结果");

        // 主动触发扫描完成信号，显示当前结果
        onScanCompleted();

        return;
    }

    // 如果当前不在扫描中，开始新的扫描
    LOG_DEBUG("Starting rescan operation");

    // 获取锁以确保同一时间只有一个操作在执行
    QMutexLocker locker(&operationMutex);

    // 禁用重新扫描按钮并阻止表格交互
    m_blockInteraction = true;

    // 检查是否有视频在播放（检查是否有选中的复选框）
    QList<QCheckBox *> activeCheckboxes;
    QList<QString> activeRtspUrls;

    for (int i = 0; i < m_cameraTableWidget->rowCount(); i++)
    {
        QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(i, OperationColumn);
        if (checkboxWidget)
        {
            QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
            if (checkbox && checkbox->isChecked())
            {
                activeCheckboxes.append(checkbox);
                activeRtspUrls.append(checkbox->property("rtspUrl").toString());
                LOG_DEBUG("Found active camera: " << checkbox->property("rtspUrl").toString());
            }
        }
    }

    // 只有在有视频播放时才显示确认对话框
    if (!activeCheckboxes.isEmpty())
    {
        LOG_DEBUG("Found " << activeCheckboxes.size() << " active cameras, showing confirmation dialog");

        // 添加确认对话框，提示用户将关闭所有视频
        QMessageBox msgBox;
        msgBox.setWindowTitle("重新扫描确认");
        msgBox.setFixedSize(280, 150);

        // 设置样式表，与截图提示框保持一致
        msgBox.setStyleSheet(
            "QMessageBox {"
            "  background-color: white;"
            "}"
            "QLabel {"
            "  color: #333333;"
            "  font-size: 12px;"
            "}"
            "QPushButton {"
            "  background-color: #0078D7;"
            "  color: white;"
            "  border: none;"
            "  padding: 6px 16px;"
            "  border-radius: 3px;"
            "  min-width: 80px;"
            "}"
            "QPushButton:hover {"
            "  background-color: #1683D8;"
            "}");

        msgBox.setText("重新扫描将关闭所有正在播放的视频。");
        msgBox.setInformativeText("是否继续？");
        msgBox.setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
        msgBox.setDefaultButton(QMessageBox::Cancel);
        msgBox.setIcon(QMessageBox::Warning);

        // 设置按钮文本
        msgBox.button(QMessageBox::Ok)->setText("确定");
        msgBox.button(QMessageBox::Cancel)->setText("取消");

        // 如果用户点击确定，才进行扫描
        if (msgBox.exec() == QMessageBox::Ok)
        {
            LOG_DEBUG("User confirmed rescan, closing " << activeCheckboxes.size() << " active cameras");

            // 先关闭所有选中的摄像头
            for (int i = 0; i < activeCheckboxes.size(); i++)
            {
                QCheckBox *checkbox = activeCheckboxes[i];
                QString rtspUrl = activeRtspUrls[i];

                // 确保取消勾选操作不会立即触发onOperationButtonClicked
                checkbox->blockSignals(true);
                checkbox->setChecked(false);
                checkbox->blockSignals(false);

                // 直接发送信号以关闭摄像头
                emit cameraDeselected(rtspUrl);

                // 更新数据库中的勾选状态
                CameraInfo camera;
                QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();
                for (const CameraInfo &cam : cameras)
                {
                    if (cam.rtspUrl == rtspUrl)
                    {
                        camera = cam;
                        break;
                    }
                }

                // 更新勾选状态并保存
                if (!camera.rtspUrl.isEmpty())
                {
                    camera.isChecked = false;
                    ConfigManager::instance().saveCameraToDatabase(camera);
                    LOG_DEBUG("Updated database checked status to false for: " << rtspUrl);
                }

                LOG_DEBUG("Closed camera for rescan: " << rtspUrl);
            }

            // 延迟一段时间再开始扫描，确保所有视频都已关闭
            QTimer *scanDelayTimer = new QTimer(this);
            scanDelayTimer->setSingleShot(true);
            connect(scanDelayTimer, &QTimer::timeout, this, [this, scanDelayTimer]()
                    {
                LOG_DEBUG("Starting scan after delay");
                startScanning();
                // 不在这里启用按钮，留给scan完成后自动启用
                scanDelayTimer->deleteLater(); });
            scanDelayTimer->start(1000); // 增加到1秒的延迟
        }
        else
        {
            LOG_DEBUG("User cancelled rescan");

            // 用户取消了操作，重新启用按钮和交互
            m_blockInteraction = false;
        }
    }
    else
    {
        LOG_DEBUG("No active cameras, starting scan directly");

        // 没有视频在播放，直接开始扫描
        startScanning();
        // 不在这里启用按钮，留给scan完成后自动启用
    }
}

void CameraScanWidget::onOperationButtonClicked(int state)
{
    QCheckBox *checkbox = qobject_cast<QCheckBox *>(sender());
    if (!checkbox)
        return;

    int row = checkbox->property("row").toInt();
    QString rtspUrl = checkbox->property("rtspUrl").toString();
    bool isChecked = (state == Qt::Checked);

    LOG_DEBUG("onOperationButtonClicked: row=" << row << ", rtspUrl=" << rtspUrl << ", isChecked=" << isChecked);

    // 使用精确的锁范围
    {
        QMutexLocker locker(&operationMutex);

        // 更新状态列
        updateCameraStatus(row, isChecked ? "在线" : "离线");

        // 更新数据库中的勾选状态
        // 先获取摄像头信息
        CameraInfo camera;
        QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();
        for (const CameraInfo &cam : cameras)
        {
            if (cam.rtspUrl == rtspUrl)
            {
                camera = cam;
                break;
            }
        }

        // 更新勾选状态并保存
        if (!camera.rtspUrl.isEmpty())
        {
            camera.isChecked = isChecked;
            ConfigManager::instance().saveCameraToDatabase(camera);
        }
    } // 锁在这里释放

    // 临时阻止用户交互，使用m_blockInteraction标志而不是禁用表格
    m_blockInteraction = true;

    // 如果勾选了，发送信号显示摄像头
    if (isChecked)
    {
        LOG_DEBUG("camera selected: " << rtspUrl);
        emit cameraSelected(rtspUrl);

        // 延迟一小段时间再启用交互
        QTimer *enableTimer = new QTimer(this);
        enableTimer->setSingleShot(true);
        connect(enableTimer, &QTimer::timeout, this, [this, enableTimer]()
                {
            m_blockInteraction = false; // 恢复交互
            enableTimer->deleteLater(); });
        enableTimer->start(300); // 300毫秒后启用
    }
    // 如果取消勾选，使用定时器延迟关闭摄像头
    else
    {
        LOG_DEBUG("camera deselected: " << rtspUrl);

        // 使用延迟处理
        QTimer *delayTimer = new QTimer(this);
        delayTimer->setSingleShot(true);
        connect(delayTimer, &QTimer::timeout, this, [this, rtspUrl, delayTimer]()
                {
                    emit cameraDeselected(rtspUrl);
                    delayTimer->deleteLater();

                    // 再增加一个定时器，延迟更长时间后再启用交互
                    QTimer *enableTimer = new QTimer(this);
                    enableTimer->setSingleShot(true);
                    connect(enableTimer, &QTimer::timeout, this, [this, enableTimer]()
                            {
                m_blockInteraction = false; // 恢复交互
                enableTimer->deleteLater(); });
                    enableTimer->start(500); // 延迟0.5秒再启用
                });
        delayTimer->start(500); // 增加延迟时间到500毫秒
    }
}

void CameraScanWidget::onItemChanged(QTableWidgetItem *item)
{
    // 只处理名称列的编辑
    if (item && item->column() == NameColumn)
    {
        int row = item->row();
        QString newName = item->text().trimmed(); // 去除前后空格
        QString rtspUrl = item->data(Qt::UserRole).toString();

        // 确保名称不为空
        if (newName.isEmpty())
        {
            // 获取当前摄像头数量，用于生成序号
            int cameraCount = m_cameraTableWidget->rowCount();
            // 使用行号+1作为序号，与数据库保持一致
            newName = QString("未命名摄像头%1").arg(row + 1);

            // 阻止信号循环
            m_cameraTableWidget->blockSignals(true);
            item->setText(newName);
            m_cameraTableWidget->blockSignals(false);
        }

        // 确保文本居中对齐
        item->setTextAlignment(Qt::AlignVCenter | Qt::AlignLeft); // 改为左对齐，与其他项保持一致
        item->setToolTip(newName);                                // 添加tooltip显示完整名称

        // 更新数据库中的名称
        ConfigManager::instance().updateCameraName(rtspUrl, newName);

        LOG_INFO("camera name updated:" << rtspUrl << "->" << newName);

        // 检查该摄像头是否已勾选
        QWidget *checkboxWidget = m_cameraTableWidget->cellWidget(row, OperationColumn);
        if (checkboxWidget)
        {
            QCheckBox *checkbox = checkboxWidget->findChild<QCheckBox *>();
            if (checkbox && checkbox->isChecked())
            {
                // 如果摄像头已勾选，发送名称更新信号
                LOG_DEBUG("Emitting camera name updated signal for: " << rtspUrl);
                emit cameraNameUpdated(rtspUrl, newName);
            }
        }

        // 确保编辑后不保持选中状态并取消焦点
        QTimer::singleShot(100, this, [this]()
                           { 
            m_cameraTableWidget->clearSelection();
            m_cameraTableWidget->clearFocus(); });
    }
}

// 添加resizeEvent处理函数
void CameraScanWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 所有列已设置为固定宽度，不需要在这里调整
}

// 实现事件过滤器
bool CameraScanWidget::eventFilter(QObject *watched, QEvent *event)
{
    // 如果是表格控件且当前处于阻止交互状态
    if (watched == m_cameraTableWidget && m_blockInteraction)
    {
        // 拦截所有鼠标事件
        if (event->type() == QEvent::MouseButtonPress ||
            event->type() == QEvent::MouseButtonDblClick ||
            event->type() == QEvent::MouseButtonRelease)
        {
            LOG_DEBUG("阻止用户交互");
            return true; // 拦截事件
        }
    }

    // 其他情况正常处理
    return QWidget::eventFilter(watched, event);
}
