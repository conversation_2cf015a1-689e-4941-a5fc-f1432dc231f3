﻿#include "mainwindow.h"
#include "onevideo.h"
#include "camerascanwidget.h"
#include "configdialog.h"
#include "configmanager.h"
#include "logmanager.h"

#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QStatusBar>
#include <QScrollArea>
#include <QPushButton>
#include <QMessageBox>
#include <QTimer>
#include <QDateTime>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QButtonGroup>
#include <QToolButton>
#include <QStackedLayout>
#include <QMouseEvent>
#include <QApplication>
#include <QScreen>

#include <QResizeEvent>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent), mousePressed(false)
{
    // 从配置文件中读取默认分屏模式
    int defaultMode = ConfigManager::instance().getDefaultDisplayMode();
    if (defaultMode == 1 || defaultMode == 4 || defaultMode == 9)
    {
        currentDisplayMode = static_cast<DisplayMode>(defaultMode);
    }
    else
    {
        currentDisplayMode = FourMode; // 如果配置文件中的值无效，使用四分屏作为默认值
    }

    resize(1200, 800);
    setMinimumSize(OneVideo::WIDTH + 320, OneVideo::HEIGHT + 50);

    // 设置无边框窗口
    setWindowFlags(Qt::FramelessWindowHint);

    // 设置窗口背景图片
    QPixmap backgroundPixmap(":/images/background.png");
    QPalette palette;
    palette.setBrush(QPalette::Window, backgroundPixmap);
    this->setPalette(palette);
    this->setAutoFillBackground(true);

    // 创建标题栏按钮
    QWidget *titleButtonsWidget = new QWidget(this);
    QHBoxLayout *titleButtonsLayout = new QHBoxLayout(titleButtonsWidget);
    titleButtonsLayout->setContentsMargins(0, 0, 5, 0);
    titleButtonsLayout->setSpacing(5);

    // 创建最小化按钮
    QPushButton *minButton = new QPushButton("─", titleButtonsWidget);
    minButton->setFixedSize(24, 24);
    minButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                             "QPushButton:hover { background-color: rgba(255, 255, 255, 30); } "
                             "QPushButton:pressed { background-color: rgba(255, 255, 255, 50); }");
    connect(minButton, &QPushButton::clicked, this, &MainWindow::showMinimized);

    // 创建最大化按钮
    QPushButton *maxButton = new QPushButton("□", titleButtonsWidget);
    maxButton->setFixedSize(24, 24);
    maxButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                             "QPushButton:hover { background-color: rgba(255, 255, 255, 30); } "
                             "QPushButton:pressed { background-color: rgba(255, 255, 255, 50); }");
    connect(maxButton, &QPushButton::clicked, this, &MainWindow::toggleMaximized);

    // 创建关闭按钮
    QPushButton *closeButton = new QPushButton("×", titleButtonsWidget);
    closeButton->setFixedSize(24, 24);
    closeButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                               "QPushButton:hover { background-color: rgba(255, 0, 0, 150); } "
                               "QPushButton:pressed { background-color: red; }");
    connect(closeButton, &QPushButton::clicked, this, &MainWindow::close);

    // 添加按钮到布局
    titleButtonsLayout->addWidget(minButton);
    titleButtonsLayout->addWidget(maxButton);
    titleButtonsLayout->addWidget(closeButton);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout();
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // 添加顶部banner图片
    bannerLabel = new QLabel(this);
    QPixmap bannerPixmap(":/images/banner.png");
    bannerLabel->setPixmap(bannerPixmap.scaled(this->width(), 40, Qt::IgnoreAspectRatio, Qt::SmoothTransformation));
    bannerLabel->setAlignment(Qt::AlignCenter);
    bannerLabel->setFixedHeight(40);

    // 创建标题栏布局
    QHBoxLayout *titleBarLayout = new QHBoxLayout();
    titleBarLayout->setContentsMargins(0, 0, 0, 0);
    titleBarLayout->setSpacing(0);

    // 添加标题文字
    titleLabel = new QLabel(ConfigManager::instance().getAppName(), this);
    titleLabel->setStyleSheet("QLabel { color: white; font-weight: bold; font-size: 22px; text-align: center; }");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setFixedWidth(this->width() * 0.6);

    // 将标题栏组件添加到标题栏布局，使用绝对定位
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(titleButtonsWidget);
    buttonLayout->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

    // 创建一个新的布局来包含标题和按钮
    QGridLayout *gridLayout = new QGridLayout();
    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->addWidget(titleLabel, 0, 1, Qt::AlignHCenter | Qt::AlignVCenter);
    gridLayout->addLayout(buttonLayout, 0, 2, Qt::AlignRight | Qt::AlignVCenter);
    gridLayout->setColumnStretch(0, 1);
    gridLayout->setColumnStretch(1, 2);
    gridLayout->setColumnStretch(2, 1);

    // 设置标题栏布局
    titleBarLayout->addLayout(gridLayout);

    // 创建标题栏容器
    QWidget *titleBarContainer = new QWidget(this);
    titleBarContainer->setLayout(titleBarLayout);
    titleBarContainer->setFixedHeight(40);
    titleBarContainer->setMouseTracking(true);
    titleBarContainer->installEventFilter(this);

    // 将标题栏容器叠加在banner上
    QStackedLayout *bannerStackLayout = new QStackedLayout();
    bannerStackLayout->setStackingMode(QStackedLayout::StackAll);
    bannerStackLayout->addWidget(bannerLabel);
    bannerStackLayout->addWidget(titleBarContainer);

    QWidget *bannerStackWidget = new QWidget(this);
    bannerStackWidget->setLayout(bannerStackLayout);

    // 添加banner到布局
    mainLayout->addWidget(bannerStackWidget);

    // 创建中央窗口部件
    QWidget *centralWidget = new QWidget(this);
    centralWidget->setLayout(mainLayout);

    // 添加分割器到主布局
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(mainSplitter, 1); // 分割器占据剩余空间

    setCentralWidget(centralWidget);

    // 不再创建菜单栏
    // createMenu();
    setStatusBar(new QStatusBar(this));
    createContent();

    // 设置分割器比例
    mainSplitter->setStretchFactor(0, 1); // 左侧占比小
    mainSplitter->setStretchFactor(1, 5); // 右侧占比大，从3改为5

    // 设置分割器初始位置
    QList<int> sizes;
    sizes << 350 << 1150; // 左侧350像素（原来250增加100），右侧1150像素（原来1250减少100）
    mainSplitter->setSizes(sizes);

    timer = new QTimer(this);
    connect(timer, SIGNAL(timeout()), SLOT(timerSlot()));
    timer->start(1000);

    // 保存窗口位置和大小，用于恢复
    normalGeometry = geometry();
}

MainWindow::~MainWindow()
{
    timer->deleteLater();
    // 直接清理资源，而不是调用removeWindowSlot
    for (OneVideo *one : showList)
    {
        one->deleteLater();
    }
    showList.clear();
}

void MainWindow::createMenu()
{
    // 菜单栏
    QMenuBar *menuBar = new QMenuBar(this);

    QMenu *menuFile = new QMenu("文件(&F)", menuBar);
    // QMenu *menuSetting = new QMenu("设置(&S)", menuBar);
    QMenu *menuHelp = new QMenu("帮助(&H)", menuBar);
    menuBar->addAction(menuFile->menuAction());
    // menuBar->addAction(menuSetting->menuAction());
    menuBar->addAction(menuHelp->menuAction());
    setMenuBar(menuBar);

    // QAction *actionAdd = new QAction("添加窗口(&A)", menuFile);
    // QAction *actionRemove = new QAction("删除窗口(&D)", menuFile);
    QAction *actionExit = new QAction("退出(&Q)", menuFile);
    // QAction *actionConfig = new QAction("扫描配置(&C)", menuSetting);
    QAction *actionAbout = new QAction("关于(&A)", menuHelp);
    // menuFile->addAction(actionAdd);
    // menuFile->addAction(actionRemove);
    // menuFile->addSeparator();
    menuFile->addAction(actionExit);
    // menuSetting->addAction(actionConfig);
    menuHelp->addAction(actionAbout);

    // connect(actionAdd, SIGNAL(triggered(bool)), SLOT(addWindowSlot()));
    // connect(actionRemove, SIGNAL(triggered(bool)), SLOT(removeWindowSlot()));
    connect(actionExit, SIGNAL(triggered(bool)), SLOT(close()));
    // connect(actionConfig, SIGNAL(triggered(bool)), SLOT(showConfigDialog()));
    connect(actionAbout, SIGNAL(triggered(bool)), SLOT(aboutSlot()));
}

void MainWindow::createContent()
{
    // 主分割器已在构造函数中创建

    // 创建左侧摄像头扫描界面
    cameraScanWidget = new CameraScanWidget(mainSplitter);
    cameraScanWidget->setMinimumWidth(350);
    mainSplitter->addWidget(cameraScanWidget);

    // 创建右侧视频显示区域
    videoContainer = new QWidget(mainSplitter);
    videoContainer->setStyleSheet("QWidget { background-image: url(:/images/monitor_bg.png); background-position: center; background-repeat: no-repeat; background-size: cover; }");
    QVBoxLayout *videoLayout = new QVBoxLayout(videoContainer);
    videoLayout->setContentsMargins(0, 0, 0, 0);
    videoLayout->setSpacing(0);

    // 添加监控画面标题
    QLabel *monitorTitleLabel = new QLabel("监控画面", videoContainer);
    // 确保与摄像头配置标题样式完全一致，使用完全相同的样式
    monitorTitleLabel->setStyleSheet("QLabel { color: #FFFFFF; background-color: #003366; padding: 5px; font-weight: bold; font-size: 14px; }");
    monitorTitleLabel->setAlignment(Qt::AlignCenter);
    monitorTitleLabel->setFixedHeight(30);
    videoLayout->addWidget(monitorTitleLabel);

    // 创建显示模式切换栏
    createDisplayModeButtons();
    videoLayout->addWidget(displayModeBar);

    mainSplitter->addWidget(videoContainer);

    // 创建视频容器直接作为videoLayout的子部件，不使用滚动区域
    mainContent = new QWidget(videoContainer);
    mainContent->setStyleSheet("background: transparent;"); // 透明背景，继承父窗口的背景
    videoLayout->addWidget(mainContent, 1);                 // 占据剩余空间

    // 添加按钮 - 隐藏添加按钮
    addBtn = new QPushButton("+", mainContent);
    addBtn->setGeometry(0, 0, OneVideo::WIDTH, OneVideo::HEIGHT);
    addBtn->setFont(QFont("黑体", 200, 87));
    addBtn->setVisible(false); // 设置为不可见

    // 连接信号槽 - 注释掉添加按钮的信号连接
    // connect(addBtn, SIGNAL(clicked(bool)), SLOT(addWindowSlot()));
    connect(cameraScanWidget, &CameraScanWidget::cameraSelected, this, &MainWindow::onCameraSelected);
    connect(cameraScanWidget, &CameraScanWidget::cameraDeselected, this, &MainWindow::onCameraDeselected);
    connect(cameraScanWidget, &CameraScanWidget::cameraNameUpdated, this, &MainWindow::onCameraNameUpdated);
}

void MainWindow::createDisplayModeButtons()
{
    // 创建显示模式切换栏
    displayModeBar = new QWidget();
    displayModeBar->setFixedHeight(50);
    QHBoxLayout *modeLayout = new QHBoxLayout(displayModeBar);
    modeLayout->setContentsMargins(5, 0, 5, 0);
    modeLayout->setSpacing(5);

    // 添加左侧弹簧使按钮组居中
    modeLayout->addStretch();

    // 创建按钮组
    displayModeGroup = new QButtonGroup(this);

    // 创建垂直布局来包含按钮和标签
    QVBoxLayout *singleLayout = new QVBoxLayout();
    singleLayout->setContentsMargins(0, 0, 0, 0);
    singleLayout->setSpacing(2);
    QWidget *singleWidget = new QWidget();
    singleWidget->setLayout(singleLayout);

    // 单画面按钮
    singleModeBtn = new QToolButton();
    singleModeBtn->setIcon(QIcon(":/images/1_def.png"));
    singleModeBtn->setIconSize(QSize(25, 25));
    singleModeBtn->setToolTip("单画面");
    singleModeBtn->setCheckable(true);
    singleModeBtn->setChecked(currentDisplayMode == SingleMode); // 根据当前模式设置选中状态
    singleModeBtn->setAutoExclusive(true);
    singleModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(singleModeBtn, SingleMode);

    // 添加单画面文字标签
    singleLabel = new QLabel("单画面");
    singleLabel->setAlignment(Qt::AlignCenter);
    singleLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    singleLayout->addWidget(singleModeBtn, 0, Qt::AlignCenter);
    singleLayout->addWidget(singleLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(singleWidget);

    // 四画面布局
    QVBoxLayout *fourLayout = new QVBoxLayout();
    fourLayout->setContentsMargins(0, 0, 0, 0);
    fourLayout->setSpacing(2);
    QWidget *fourWidget = new QWidget();
    fourWidget->setLayout(fourLayout);

    // 4画面按钮
    fourModeBtn = new QToolButton();
    fourModeBtn->setIcon(QIcon(":/images/4_def.png"));
    fourModeBtn->setIconSize(QSize(25, 25));
    fourModeBtn->setToolTip("4画面");
    fourModeBtn->setCheckable(true);
    fourModeBtn->setChecked(currentDisplayMode == FourMode); // 根据当前模式设置选中状态
    fourModeBtn->setAutoExclusive(true);
    fourModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(fourModeBtn, FourMode);

    // 添加四画面文字标签
    fourLabel = new QLabel("四画面");
    fourLabel->setAlignment(Qt::AlignCenter);
    fourLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    fourLayout->addWidget(fourModeBtn, 0, Qt::AlignCenter);
    fourLayout->addWidget(fourLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(fourWidget);

    // 九画面布局
    QVBoxLayout *nineLayout = new QVBoxLayout();
    nineLayout->setContentsMargins(0, 0, 0, 0);
    nineLayout->setSpacing(2);
    QWidget *nineWidget = new QWidget();
    nineWidget->setLayout(nineLayout);

    // 9画面按钮
    nineModeBtn = new QToolButton();
    nineModeBtn->setIcon(QIcon(":/images/9_def.png"));
    nineModeBtn->setIconSize(QSize(25, 25));
    nineModeBtn->setToolTip("9画面");
    nineModeBtn->setCheckable(true);
    nineModeBtn->setChecked(currentDisplayMode == NineMode); // 根据当前模式设置选中状态
    nineModeBtn->setAutoExclusive(true);
    nineModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(nineModeBtn, NineMode);

    // 添加九画面文字标签
    nineLabel = new QLabel("九画面");
    nineLabel->setAlignment(Qt::AlignCenter);
    nineLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    nineLayout->addWidget(nineModeBtn, 0, Qt::AlignCenter);
    nineLayout->addWidget(nineLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(nineWidget);

    // 添加右侧弹簧使按钮组居中
    modeLayout->addStretch();

    // 连接信号槽
    connect(displayModeGroup, SIGNAL(buttonClicked(int)), this, SLOT(changeDisplayMode(int)));

    // 设置样式
    QString btnStyle = "QToolButton { background: transparent; border: none; }";
    singleModeBtn->setStyleSheet(btnStyle);
    fourModeBtn->setStyleSheet(btnStyle);
    nineModeBtn->setStyleSheet(btnStyle);

    // 设置初始状态图标
    updateDisplayModeIcons();
}

void MainWindow::updateDisplayModeIcons()
{
    // 根据按钮状态更新图标
    singleModeBtn->setIcon(QIcon(singleModeBtn->isChecked() ? ":/images/1.png" : ":/images/1_def.png"));
    fourModeBtn->setIcon(QIcon(fourModeBtn->isChecked() ? ":/images/4.png" : ":/images/4_def.png"));
    nineModeBtn->setIcon(QIcon(nineModeBtn->isChecked() ? ":/images/9.png" : ":/images/9_def.png"));

    // 更新文字标签颜色
    singleLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                   .arg(singleModeBtn->isChecked() ? "#00FFFF" : "white"));
    fourLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                 .arg(fourModeBtn->isChecked() ? "#00FFFF" : "white"));
    nineLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                 .arg(nineModeBtn->isChecked() ? "#00FFFF" : "white"));
}

void MainWindow::changeDisplayMode(int mode)
{
    // 更新当前显示模式
    currentDisplayMode = static_cast<DisplayMode>(mode);

    // 更新按钮图标
    updateDisplayModeIcons();

    // 重新布局视频窗口
    layoutChild();
}

void MainWindow::layoutChild()
{
    // 获取可用空间
    int availableWidth = mainContent->width();
    int availableHeight = mainContent->height();

    // 计算每个视频窗口的大小
    int videoCount = showList.size();
    if (videoCount == 0)
        return;

    // 根据当前显示模式确定布局方式
    int cols;
    int rows;

    switch (currentDisplayMode)
    {
    case SingleMode:
        cols = 1;
        rows = 1;
        break;
    case FourMode:
        cols = 2;
        rows = 2;
        break;
    case NineMode:
        cols = 3;
        rows = 3;
        break;
    default:
        cols = 2;                              // 默认为2列
        rows = (videoCount + cols - 1) / cols; // 向上取整计算行数
    }

    // 设置视频间距
    const int spacing = 10; // 视频之间的间距
    const int margin = 10;  // 边缘间距

    // 计算每个视频窗口的宽高（考虑间距）
    int videoWidth = (availableWidth - (spacing * (cols - 1)) - (margin * 2)) / cols;
    int videoHeight = (availableHeight - (spacing * (rows - 1)) - (margin * 2)) / rows;

    // 如果是单画面模式且只有一个视频，让它占据整个区域但保留边距
    if (currentDisplayMode == SingleMode && videoCount > 0)
    {
        // 只显示第一个视频，隐藏其他视频
        for (int i = 0; i < videoCount; ++i)
        {
            if (i == 0)
            {
                // 在单画面模式下添加边距
                showList[i]->setGeometry(margin, margin,
                                         availableWidth - (margin * 2),
                                         availableHeight - (margin * 2));
                showList[i]->setVisible(true);
            }
            else
            {
                showList[i]->setVisible(false);
            }
        }
        return;
    }

    // 布局多个视频窗口
    for (int i = 0; i < videoCount; ++i)
    {
        // 根据当前模式决定是否显示
        bool shouldShow = true;

        if ((currentDisplayMode == FourMode && i >= 4) ||
            (currentDisplayMode == NineMode && i >= 9))
        {
            shouldShow = false;
        }

        if (shouldShow)
        {
            int row = i / cols;
            int col = i % cols;

            // 计算位置时考虑间距和边距
            int x = margin + col * (videoWidth + spacing);
            int y = margin + row * (videoHeight + spacing);

            // 设置视频窗口的位置和大小
            showList[i]->setGeometry(x, y, videoWidth, videoHeight);
            showList[i]->setVisible(true);
        }
        else
        {
            showList[i]->setVisible(false);
        }
    }
}

void MainWindow::addWindowSlot()
{
    OneVideo *one = new OneVideo(mainContent);
    connect(one, SIGNAL(closeSignal(OneVideo *)), SLOT(childClosed(OneVideo *)));
    one->show();
    showList.append(one);

    layoutChild();
}

void MainWindow::removeWindowSlot()
{
    for (OneVideo *one : showList)
    {
        one->deleteLater();
    }
    showList.clear();
    layoutChild();
}

void MainWindow::aboutSlot()
{
    QMessageBox::aboutQt(this);
}

void MainWindow::showConfigDialog()
{
    ConfigDialog dialog(this);
    dialog.exec();
}

void MainWindow::timerSlot()
{
    // 移除时间显示
    statusBar()->clearMessage();
}

void MainWindow::childClosed(OneVideo *who)
{
    // 从映射中查找并移除
    QString rtspToRemove;
    for (auto it = rtspVideoMap.begin(); it != rtspVideoMap.end(); ++it)
    {
        if (it.value() == who)
        {
            rtspToRemove = it.key();
            break;
        }
    }

    if (!rtspToRemove.isEmpty())
    {
        rtspVideoMap.remove(rtspToRemove);
    }

    showList.removeOne(who);
    layoutChild();
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 更新顶部banner图片大小
    if (bannerLabel)
    {
        QPixmap bannerPixmap(":/images/banner.png");
        bannerLabel->setPixmap(bannerPixmap.scaled(this->width(), 40, Qt::IgnoreAspectRatio, Qt::SmoothTransformation));
    }

    // 更新标题宽度
    if (titleLabel)
    {
        titleLabel->setFixedWidth(this->width() * 0.6); // 设置标题宽度为窗口宽度的60%
    }

    layoutChild(); // 重新布局视频窗口
}

// 当在扫描界面选择摄像头时，自动创建一个新的视频窗口并连接
void MainWindow::onCameraSelected(const QString &rtspUrl)
{
    // 如果URL不为空，连接到摄像头
    if (!rtspUrl.isEmpty())
    {
        // 检查是否已经有该URL对应的视频窗口
        if (rtspVideoMap.contains(rtspUrl))
        {
            // 该摄像头已经显示，不需要重复创建
            return;
        }

        // 创建新窗口
        OneVideo *targetVideo = new OneVideo(mainContent);
        connect(targetVideo, SIGNAL(closeSignal(OneVideo *)), SLOT(childClosed(OneVideo *)));
        targetVideo->show();
        showList.append(targetVideo);

        // 记录RTSP URL与视频窗口的映射关系
        rtspVideoMap[rtspUrl] = targetVideo;

        // 重新布局
        layoutChild();

        // 获取摄像头信息，找出名称
        QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();
        for (const CameraInfo &camera : cameras)
        {
            if (camera.rtspUrl == rtspUrl && !camera.name.isEmpty())
            {
                // 设置摄像头名称
                targetVideo->setCameraName(camera.name);
                LOG_INFO("set camera name: " << camera.name);
                break;
            }
        }

        // 设置RTSP URL并开始播放
        targetVideo->setRtspUrl(rtspUrl);

        LOG_INFO("show camera: " << rtspUrl);
    }
}

// 当在扫描界面取消选择摄像头时，关闭对应的视频窗口
void MainWindow::onCameraDeselected(const QString &rtspUrl)
{
    // 如果URL不为空，关闭对应的视频窗口
    if (!rtspUrl.isEmpty() && rtspVideoMap.contains(rtspUrl))
    {
        OneVideo *targetVideo = rtspVideoMap[rtspUrl];

        // 首先停止视频线程
        targetVideo->stopNetwork();

        // 从映射中移除，先移除映射关系，防止回调时再次访问
        rtspVideoMap.remove(rtspUrl);

        // 从列表中移除
        showList.removeOne(targetVideo);

        // 使用延长的定时器确保线程有足够的时间停止
        QTimer *delayTimer = new QTimer(this);
        delayTimer->setSingleShot(true);
        connect(delayTimer, &QTimer::timeout, [targetVideo, delayTimer]()
                {
            // 关闭视频窗口并释放资源
            targetVideo->close();
            targetVideo->deleteLater();
            delayTimer->deleteLater(); });
        delayTimer->start(1000); // 增加到1秒

        // 重新布局
        layoutChild();

        LOG_INFO("close camera: " << rtspUrl);
    }
}

// 处理摄像头名称更新
void MainWindow::onCameraNameUpdated(const QString &rtspUrl, const QString &newName)
{
    // 如果URL不为空且存在对应的视频窗口，更新摄像头名称
    if (!rtspUrl.isEmpty() && rtspVideoMap.contains(rtspUrl))
    {
        OneVideo *targetVideo = rtspVideoMap[rtspUrl];

        // 更新摄像头名称
        targetVideo->setCameraName(newName);

        LOG_INFO("update camera name: " << rtspUrl << " -> " << newName);
    }
}

void MainWindow::toggleMaximized()
{
    if (isMaximized())
    {
        // 还原窗口
        showNormal();
    }
    else
    {
        // 保存当前窗口位置和大小
        normalGeometry = geometry();
        // 最大化窗口
        showMaximized();
    }
}

bool MainWindow::eventFilter(QObject *watched, QEvent *event)
{
    // 处理标题栏的鼠标事件
    if (watched->parent() == this)
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton)
            {
                dragPosition = mouseEvent->globalPos() - frameGeometry().topLeft();
                mousePressed = true;
                return true;
            }
        }
        else if (event->type() == QEvent::MouseMove)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->buttons() & Qt::LeftButton && mousePressed)
            {
                move(mouseEvent->globalPos() - dragPosition);
                return true;
            }
        }
        else if (event->type() == QEvent::MouseButtonRelease)
        {
            mousePressed = false;
        }
        else if (event->type() == QEvent::MouseButtonDblClick)
        {
            // 双击标题栏切换最大化/还原
            toggleMaximized();
            return true;
        }
    }
    return QMainWindow::eventFilter(watched, event);
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        dragPosition = event->globalPos() - frameGeometry().topLeft();
        mousePressed = true;
    }
    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton && mousePressed)
    {
        // 只有在标题栏区域拖动时才移动窗口
        if (event->pos().y() < 40)
        {
            move(event->globalPos() - dragPosition);
        }
    }
    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    mousePressed = false;
    QMainWindow::mouseReleaseEvent(event);
}
