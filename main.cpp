#include "mainwindow.h"
#include "configmanager.h"
#include "logmanager.h"
#include <QApplication>
#include <QTextCodec>
#include <QMessageBox>
#include <QFileInfo>
#include <QDebug>
#include <QTimer>
#include "SingleApplication.h"
#include "zf_UdiskAndSoftKeyCheck.h"
#include "GlobalSignal.h"
#include <QFile>
#include <QDir>
#include <QProcess>
#include <QDateTime>
#include <QSettings>
#include <QScreen>
#include <QTranslator>
#include <QLibrary>

#ifdef _WIN32
#include <windows.h>
#endif

#define CHECK_POLL_TIME 60 * 2

static int UDiskCheckTimes = 3;
void cbUCheck(int nState, void *)
{
	if (nState != RET_PassValidation)
	{
		UDiskCheckTimes--;
		if (UDiskCheckTimes <= 1)
		{
			GlobalSignal::getInstance().ukeyPullOut(UDiskCheckTimes);
		}
	}
	else
	{
		UDiskCheckTimes = 3;
	}
}

// 临时禁用日志宏，直接使用qDebug
#define TEMP_LOG(msg) qDebug() << msg

// 应用程序退出时的清理函数
void cleanupResources()
{
	TEMP_LOG("应用程序退出，关闭日志系统");
	LogManager::instance().close();
}

// 检查FFmpeg DLL是否存在
bool checkFFmpegDlls()
{
#ifdef USE_FFMPEG
	QStringList requiredDlls = {
		"avformat-59.dll",
		"avcodec-59.dll",
		"avutil-57.dll",
		"swscale-6.dll",
		"swresample-4.dll"};

	QString appDir = QCoreApplication::applicationDirPath();
	bool allExist = true;

	for (const QString &dll : requiredDlls)
	{
		QString fullPath = appDir + "/" + dll;
		bool exists = QFileInfo::exists(fullPath);
		qDebug() << (exists ? "找到" : "未找到") << dll;
		if (!exists)
		{
			allExist = false;
		}
	}

	return allExist;
#else
	return true;
#endif
}

int main(int argc, char *argv[])
{
	TEMP_LOG("程序启动");

#ifdef _WIN32
	// 设置DLL搜索路径为应用程序目录，确保能找到FFmpeg DLL
	SetDllDirectoryA(QCoreApplication::applicationDirPath().toLocal8Bit().constData());
#endif

	// 设置编码
#if (QT_VERSION <= QT_VERSION_CHECK(5, 0, 0))
	QTextCodec::setCodecForTr(QTextCodec::codecForName("UTF-8"));
	QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
	QTextCodec::setCodecForCStrings(QTextCodec::codecForName("UTF-8"));
#else
	QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif

	// 创建应用程序对象
	SingleApplication app(argc, argv);
	if (app.isRunning())
	{
		TEMP_LOG("程序已运行，退出");
		QMessageBox::information(Q_NULLPTR, "提示", "程序已运行");
		exit(0);
	}

#ifdef USE_FFMPEG
	// 检查FFmpeg DLL是否存在
	bool ffmpegDllsExist = checkFFmpegDlls();
	if (!ffmpegDllsExist)
	{
		TEMP_LOG("警告：某些FFmpeg DLL文件缺失，可能影响视频功能");
	}
	else
	{
		TEMP_LOG("FFmpeg DLL检查通过");
	}
#endif

	// 设置应用程序信息
	TEMP_LOG("设置应用程序信息");
	QApplication::setOrganizationName(ConfigManager::instance().getAppName());
	// 从配置文件中读取应用程序名称
	QApplication::setApplicationName(ConfigManager::instance().getAppName());

	// 注册退出清理函数
	qAddPostRoutine(cleanupResources);

	// 初始化配置管理器 - 这会触发配置文件的加载
	TEMP_LOG("初始化配置管理器");
	try
	{
		ConfigManager::instance();
		TEMP_LOG("配置管理器初始化成功");
	}
	catch (const std::exception &e)
	{
		TEMP_LOG("配置管理器初始化异常: " << e.what());
	}
	catch (...)
	{
		TEMP_LOG("配置管理器初始化未知异常");
	}

	// 初始化日志系统
	TEMP_LOG("初始化日志系统");
	bool logInitSuccess = false;
	try
	{
		logInitSuccess = LogManager::instance().init();
		if (!logInitSuccess)
		{
			TEMP_LOG("日志系统初始化失败");
		}
		else
		{
			TEMP_LOG("日志系统初始化成功");
		}
	}
	catch (const std::exception &e)
	{
		TEMP_LOG("日志系统初始化异常: " << e.what());
	}
	catch (...)
	{
		TEMP_LOG("日志系统初始化未知异常");
	}

	// 继续执行验证等操作
	TEMP_LOG("执行验证操作");
	QString exeFileName = QCoreApplication::applicationFilePath();
	QFileInfo file(exeFileName);
	QString app_path = file.canonicalPath();
	QString str = "XX-S000029";
	QString strDBPath = app_path + "/Conf.db";
	ParamCustom param;
	param.pCallBack = cbUCheck;
	param.param = NULL;

	TEMP_LOG("调用UdiskVerificationAndScan");

	int nRet = UdiskVerificationAndScan(str.toUtf8().data(), CHECK_POLL_TIME, true, &param);
	if (nRet != RET_PassValidation)
	{
		TEMP_LOG("UdiskVerificationAndScan验证失败，调用CheckSoftKeyWithDlg");

		nRet = CheckSoftKeyWithDlg(str.toUtf8().data(),
								   strDBPath.toUtf8().data());
		if (nRet != RET_PassValidation)
		{
			TEMP_LOG("CheckSoftKeyWithDlg验证失败，退出程序");
			//            QMessageBox::about(NULL,strName,"授权码验证失败!!");
			exit(0);
			return 0;
		}
	}

	// 记录应用程序启动日志
	if (logInitSuccess)
	{
		TEMP_LOG("记录应用程序启动日志");
		LOG_INFO("应用程序启动");
	}

	// 创建并显示主窗口
	TEMP_LOG("创建主窗口");
	MainWindow *w = new MainWindow();

	TEMP_LOG("显示主窗口");

	// 显示主窗口 - 使用延迟显示，确保事件循环已经启动
	QTimer::singleShot(100, w, SLOT(show()));

	TEMP_LOG("进入事件循环");

	// 进入事件循环
	return app.exec();
}
