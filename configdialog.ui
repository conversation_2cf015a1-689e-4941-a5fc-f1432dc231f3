<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ConfigDialog</class>
 <widget class="QDialog" name="ConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>400</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>400</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>500</width>
    <height>400</height>
   </size>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::DefaultContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>扫描配置</string>
  </property>
  <property name="whatsThis">
   <string/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tabScan">
      <attribute name="title">
       <string>扫描设置</string>
      </attribute>
      <layout class="QFormLayout" name="formLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="label_1">
         <property name="text">
          <string>IP前缀:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="ipPrefixEdit"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>起始IP:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QSpinBox" name="startIpBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>254</number>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>结束IP:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QSpinBox" name="endIpBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>254</number>
         </property>
         <property name="value">
          <number>254</number>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabAuth">
      <attribute name="title">
       <string>认证设置</string>
      </attribute>
      <layout class="QFormLayout" name="formLayout_2">
       <item row="0" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>用户名:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="usernameEdit"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>密码:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="passwordEdit">
         <property name="echoMode">
          <enum>QLineEdit::Password</enum>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>流路径:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="streamPathEdit"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabTimeout">
      <attribute name="title">
       <string>超时设置</string>
      </attribute>
      <layout class="QFormLayout" name="formLayout_3">
       <item row="0" column="0">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>RTSP超时(ms):</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QSpinBox" name="rtspTimeoutBox">
         <property name="minimum">
          <number>500</number>
         </property>
         <property name="maximum">
          <number>10000</number>
         </property>
         <property name="singleStep">
          <number>100</number>
         </property>
         <property name="value">
          <number>1000</number>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="2">
        <widget class="QCheckBox" name="useHttpDetectionCheck">
         <property name="text">
          <string>使用HTTP图像检测</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>HTTP超时(ms):</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QSpinBox" name="httpTimeoutBox">
         <property name="minimum">
          <number>500</number>
         </property>
         <property name="maximum">
          <number>10000</number>
         </property>
         <property name="singleStep">
          <number>100</number>
         </property>
         <property name="value">
          <number>1000</number>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabDisplay">
      <attribute name="title">
       <string>显示设置</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QCheckBox" name="autoDisplayCheck">
         <property name="text">
          <string>自动显示新发现的摄像头</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>默认分屏数:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="defaultModeComboBox">
           <item>
            <property name="text">
             <string>1 (单画面)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4 (四画面)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>9 (九画面)</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
     <property name="centerButtons">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ConfigDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
