#ifndef VIDEOSURFACE_H
#define VIDEOSURFACE_H

#include <QAbstractVideoSurface>
#include <QVideoSurfaceFormat>
#include <QVideoFrame>
#include <QImage>

// 自定义视频表面类，用于从QMediaPlayer获取视频帧
class VideoSurface : public QAbstractVideoSurface
{
    Q_OBJECT
public:
    explicit VideoSurface(QObject *parent = nullptr);
    ~VideoSurface();

    QList<QVideoFrame::PixelFormat> supportedPixelFormats(
        QAbstractVideoBuffer::HandleType handleType = QAbstractVideoBuffer::NoHandle) const override;

    bool present(const QVideoFrame &frame) override;

signals:
    void frameAvailable(const QImage &image);

private:
    QImage frameToImage(const QVideoFrame &frame) const;
};

#endif // VIDEOSURFACE_H