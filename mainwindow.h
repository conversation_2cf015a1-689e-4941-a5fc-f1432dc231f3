﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QList>
#include <QSplitter>
#include <QMap>
#include <QTimer>
#include <QLabel>
#include <QPoint>
#include <QToolButton>
#include <QButtonGroup>

class OneVideo;
class QPushButton;
class CameraScanWidget;
class QHBoxLayout;

class MainWindow : public QMainWindow
{
	Q_OBJECT

public:
	explicit MainWindow(QWidget *parent = 0);
	~MainWindow();

	// 创建菜单
	void createMenu();

	// 创建内容
	void createContent();

	// 重新布局
	void layoutChild();

	// 显示模式枚举
	enum DisplayMode
	{
		SingleMode = 1, // 单画面模式
		FourMode = 4,	// 4画面模式
		NineMode = 9	// 9画面模式
	};

private slots:
	// 添加窗口
	void addWindowSlot();

	// 删除窗口
	void removeWindowSlot();

	// 关于
	void aboutSlot();

	// 显示配置对话框
	void showConfigDialog();

	// 定时器
	void timerSlot();

	// 子窗口关闭
	void childClosed(OneVideo *who);

	// 摄像头选择
	void onCameraSelected(const QString &rtspUrl);

	// 摄像头取消选择
	void onCameraDeselected(const QString &rtspUrl);

	// 摄像头名称更新
	void onCameraNameUpdated(const QString &rtspUrl, const QString &newName);

	// 切换显示模式
	void changeDisplayMode(int mode);

	// 切换最大化/还原窗口
	void toggleMaximized();

private:
	// 创建显示模式切换按钮
	void createDisplayModeButtons();

	// 创建界面组件
	void createActions();
	void createMenus();
	void createToolBars();
	void createStatusBar();
	void createDockWindows();
	void updateDisplayModeIcons(); // 更新显示模式图标

	// 子窗口容器
	QList<OneVideo *> showList;

	// 存储RTSP URL与对应OneVideo的映射
	QMap<QString, OneVideo *> rtspVideoMap;

	// UI元素
	QWidget *centralWidget;
	QWidget *mainContent;
	QPushButton *addBtn;
	QSplitter *mainSplitter;			// 主分割器
	CameraScanWidget *cameraScanWidget; // 摄像头扫描界面
	QWidget *videoContainer;			// 视频容器
	QWidget *displayModeBar;			// 显示模式切换栏
	QButtonGroup *displayModeGroup;		// 显示模式按钮组
	QLabel *bannerLabel;				// 顶部banner标签
	QLabel *titleLabel;					// 标题标签

	// 显示模式按钮
	QToolButton *singleModeBtn; // 单画面按钮
	QToolButton *fourModeBtn;	// 四分屏按钮
	QToolButton *nineModeBtn;	// 九分屏按钮

	// 显示模式标签
	QLabel *singleLabel; // 单画面标签
	QLabel *fourLabel;	 // 四分屏标签
	QLabel *nineLabel;	 // 九分屏标签

	// 当前显示模式
	DisplayMode currentDisplayMode;

	// 定时器
	QTimer *timer;

	// 鼠标拖动相关变量
	QPoint dragPosition;
	bool mousePressed;
	QRect normalGeometry; // 保存窗口正常状态的位置和大小

protected:
	void resizeEvent(QResizeEvent *event) Q_DECL_OVERRIDE;
	bool eventFilter(QObject *watched, QEvent *event) Q_DECL_OVERRIDE;
	void mousePressEvent(QMouseEvent *event) Q_DECL_OVERRIDE;
	void mouseMoveEvent(QMouseEvent *event) Q_DECL_OVERRIDE;
	void mouseReleaseEvent(QMouseEvent *event) Q_DECL_OVERRIDE;
};

#endif // MAINWINDOW_H
