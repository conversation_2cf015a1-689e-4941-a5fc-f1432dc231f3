## 目录结构

```
.
├── array.h         动态数组
├── hatomic.h       原子操作
├── hbase.h         基础函数
├── hbuf.h          缓存
├── hdef.h          常见宏定义
├── hdir.h          目录(ls实现)
├── heap.h          堆
├── herr.h          错误码表
├── hfile.h         文件类
├── hlog.h          日志
├── hmath.h         数学函数
├── hmutex.h        线程同步锁
├── hobjectpool.h   对象池
├── hplatform.h     平台相关宏
├── hproc.h         进程
├── hscope.h        作用域模板类
├── hsocket.h       套接字
├── hssl.h          SSL/TLS
├── hstring.h       字符串操作
├── hsysinfo.h      系统信息
├── hthread.h       线程
├── hthreadpool.h   线程池
├── htime.h         时间
├── hurl.h          URL操作
├── hversion.h      版本
├── ifconfig.h      网络配置(ifconfig实现)
├── list.h          链表
└── queue.h         队列

```
