/********************************************************************************
** Form generated from reading UI file 'configdialog.ui'
**
** Created by: Qt User Interface Compiler version 5.9.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CONFIGDIALOG_H
#define UI_CONFIGDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ConfigDialog
{
public:
    QVBoxLayout *verticalLayout;
    QTabWidget *tabWidget;
    QWidget *tabScan;
    QFormLayout *formLayout;
    QLabel *label_1;
    QLineEdit *ipPrefixEdit;
    QLabel *label_2;
    QSpinBox *startIpBox;
    QLabel *label_3;
    QSpinBox *endIpBox;
    QWidget *tabAuth;
    QFormLayout *formLayout_2;
    QLabel *label_4;
    QLineEdit *usernameEdit;
    QLabel *label_5;
    QLineEdit *passwordEdit;
    QLabel *label_6;
    QLineEdit *streamPathEdit;
    QWidget *tabTimeout;
    QFormLayout *formLayout_3;
    QLabel *label_7;
    QSpinBox *rtspTimeoutBox;
    QCheckBox *useHttpDetectionCheck;
    QLabel *label_8;
    QSpinBox *httpTimeoutBox;
    QWidget *tabDisplay;
    QVBoxLayout *verticalLayout_2;
    QCheckBox *autoDisplayCheck;
    QHBoxLayout *horizontalLayout;
    QLabel *label_9;
    QComboBox *defaultModeComboBox;
    QSpacerItem *verticalSpacer;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *ConfigDialog)
    {
        if (ConfigDialog->objectName().isEmpty())
            ConfigDialog->setObjectName(QStringLiteral("ConfigDialog"));
        ConfigDialog->resize(500, 400);
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(ConfigDialog->sizePolicy().hasHeightForWidth());
        ConfigDialog->setSizePolicy(sizePolicy);
        ConfigDialog->setMinimumSize(QSize(500, 400));
        ConfigDialog->setMaximumSize(QSize(500, 400));
        ConfigDialog->setContextMenuPolicy(Qt::DefaultContextMenu);
        verticalLayout = new QVBoxLayout(ConfigDialog);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        tabWidget = new QTabWidget(ConfigDialog);
        tabWidget->setObjectName(QStringLiteral("tabWidget"));
        tabScan = new QWidget();
        tabScan->setObjectName(QStringLiteral("tabScan"));
        formLayout = new QFormLayout(tabScan);
        formLayout->setObjectName(QStringLiteral("formLayout"));
        label_1 = new QLabel(tabScan);
        label_1->setObjectName(QStringLiteral("label_1"));

        formLayout->setWidget(0, QFormLayout::LabelRole, label_1);

        ipPrefixEdit = new QLineEdit(tabScan);
        ipPrefixEdit->setObjectName(QStringLiteral("ipPrefixEdit"));

        formLayout->setWidget(0, QFormLayout::FieldRole, ipPrefixEdit);

        label_2 = new QLabel(tabScan);
        label_2->setObjectName(QStringLiteral("label_2"));

        formLayout->setWidget(1, QFormLayout::LabelRole, label_2);

        startIpBox = new QSpinBox(tabScan);
        startIpBox->setObjectName(QStringLiteral("startIpBox"));
        startIpBox->setMinimum(1);
        startIpBox->setMaximum(254);

        formLayout->setWidget(1, QFormLayout::FieldRole, startIpBox);

        label_3 = new QLabel(tabScan);
        label_3->setObjectName(QStringLiteral("label_3"));

        formLayout->setWidget(2, QFormLayout::LabelRole, label_3);

        endIpBox = new QSpinBox(tabScan);
        endIpBox->setObjectName(QStringLiteral("endIpBox"));
        endIpBox->setMinimum(1);
        endIpBox->setMaximum(254);
        endIpBox->setValue(254);

        formLayout->setWidget(2, QFormLayout::FieldRole, endIpBox);

        tabWidget->addTab(tabScan, QString());
        tabAuth = new QWidget();
        tabAuth->setObjectName(QStringLiteral("tabAuth"));
        formLayout_2 = new QFormLayout(tabAuth);
        formLayout_2->setObjectName(QStringLiteral("formLayout_2"));
        label_4 = new QLabel(tabAuth);
        label_4->setObjectName(QStringLiteral("label_4"));

        formLayout_2->setWidget(0, QFormLayout::LabelRole, label_4);

        usernameEdit = new QLineEdit(tabAuth);
        usernameEdit->setObjectName(QStringLiteral("usernameEdit"));

        formLayout_2->setWidget(0, QFormLayout::FieldRole, usernameEdit);

        label_5 = new QLabel(tabAuth);
        label_5->setObjectName(QStringLiteral("label_5"));

        formLayout_2->setWidget(1, QFormLayout::LabelRole, label_5);

        passwordEdit = new QLineEdit(tabAuth);
        passwordEdit->setObjectName(QStringLiteral("passwordEdit"));
        passwordEdit->setEchoMode(QLineEdit::Password);

        formLayout_2->setWidget(1, QFormLayout::FieldRole, passwordEdit);

        label_6 = new QLabel(tabAuth);
        label_6->setObjectName(QStringLiteral("label_6"));

        formLayout_2->setWidget(2, QFormLayout::LabelRole, label_6);

        streamPathEdit = new QLineEdit(tabAuth);
        streamPathEdit->setObjectName(QStringLiteral("streamPathEdit"));

        formLayout_2->setWidget(2, QFormLayout::FieldRole, streamPathEdit);

        tabWidget->addTab(tabAuth, QString());
        tabTimeout = new QWidget();
        tabTimeout->setObjectName(QStringLiteral("tabTimeout"));
        formLayout_3 = new QFormLayout(tabTimeout);
        formLayout_3->setObjectName(QStringLiteral("formLayout_3"));
        label_7 = new QLabel(tabTimeout);
        label_7->setObjectName(QStringLiteral("label_7"));

        formLayout_3->setWidget(0, QFormLayout::LabelRole, label_7);

        rtspTimeoutBox = new QSpinBox(tabTimeout);
        rtspTimeoutBox->setObjectName(QStringLiteral("rtspTimeoutBox"));
        rtspTimeoutBox->setMinimum(500);
        rtspTimeoutBox->setMaximum(10000);
        rtspTimeoutBox->setSingleStep(100);
        rtspTimeoutBox->setValue(1000);

        formLayout_3->setWidget(0, QFormLayout::FieldRole, rtspTimeoutBox);

        useHttpDetectionCheck = new QCheckBox(tabTimeout);
        useHttpDetectionCheck->setObjectName(QStringLiteral("useHttpDetectionCheck"));
        useHttpDetectionCheck->setChecked(true);

        formLayout_3->setWidget(1, QFormLayout::SpanningRole, useHttpDetectionCheck);

        label_8 = new QLabel(tabTimeout);
        label_8->setObjectName(QStringLiteral("label_8"));

        formLayout_3->setWidget(2, QFormLayout::LabelRole, label_8);

        httpTimeoutBox = new QSpinBox(tabTimeout);
        httpTimeoutBox->setObjectName(QStringLiteral("httpTimeoutBox"));
        httpTimeoutBox->setMinimum(500);
        httpTimeoutBox->setMaximum(10000);
        httpTimeoutBox->setSingleStep(100);
        httpTimeoutBox->setValue(1000);

        formLayout_3->setWidget(2, QFormLayout::FieldRole, httpTimeoutBox);

        tabWidget->addTab(tabTimeout, QString());
        tabDisplay = new QWidget();
        tabDisplay->setObjectName(QStringLiteral("tabDisplay"));
        verticalLayout_2 = new QVBoxLayout(tabDisplay);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        autoDisplayCheck = new QCheckBox(tabDisplay);
        autoDisplayCheck->setObjectName(QStringLiteral("autoDisplayCheck"));
        autoDisplayCheck->setChecked(true);

        verticalLayout_2->addWidget(autoDisplayCheck);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        label_9 = new QLabel(tabDisplay);
        label_9->setObjectName(QStringLiteral("label_9"));

        horizontalLayout->addWidget(label_9);

        defaultModeComboBox = new QComboBox(tabDisplay);
        defaultModeComboBox->setObjectName(QStringLiteral("defaultModeComboBox"));

        horizontalLayout->addWidget(defaultModeComboBox);


        verticalLayout_2->addLayout(horizontalLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);

        tabWidget->addTab(tabDisplay, QString());

        verticalLayout->addWidget(tabWidget);

        buttonBox = new QDialogButtonBox(ConfigDialog);
        buttonBox->setObjectName(QStringLiteral("buttonBox"));
        buttonBox->setOrientation(Qt::Horizontal);
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);
        buttonBox->setCenterButtons(true);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(ConfigDialog);
        QObject::connect(buttonBox, SIGNAL(accepted()), ConfigDialog, SLOT(accept()));
        QObject::connect(buttonBox, SIGNAL(rejected()), ConfigDialog, SLOT(reject()));

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(ConfigDialog);
    } // setupUi

    void retranslateUi(QDialog *ConfigDialog)
    {
        ConfigDialog->setWindowTitle(QApplication::translate("ConfigDialog", "\346\211\253\346\217\217\351\205\215\347\275\256", Q_NULLPTR));
#ifndef QT_NO_WHATSTHIS
        ConfigDialog->setWhatsThis(QString());
#endif // QT_NO_WHATSTHIS
        label_1->setText(QApplication::translate("ConfigDialog", "IP\345\211\215\347\274\200:", Q_NULLPTR));
        label_2->setText(QApplication::translate("ConfigDialog", "\350\265\267\345\247\213IP:", Q_NULLPTR));
        label_3->setText(QApplication::translate("ConfigDialog", "\347\273\223\346\235\237IP:", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tabScan), QApplication::translate("ConfigDialog", "\346\211\253\346\217\217\350\256\276\347\275\256", Q_NULLPTR));
        label_4->setText(QApplication::translate("ConfigDialog", "\347\224\250\346\210\267\345\220\215:", Q_NULLPTR));
        label_5->setText(QApplication::translate("ConfigDialog", "\345\257\206\347\240\201:", Q_NULLPTR));
        label_6->setText(QApplication::translate("ConfigDialog", "\346\265\201\350\267\257\345\276\204:", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tabAuth), QApplication::translate("ConfigDialog", "\350\256\244\350\257\201\350\256\276\347\275\256", Q_NULLPTR));
        label_7->setText(QApplication::translate("ConfigDialog", "RTSP\350\266\205\346\227\266(ms):", Q_NULLPTR));
        useHttpDetectionCheck->setText(QApplication::translate("ConfigDialog", "\344\275\277\347\224\250HTTP\345\233\276\345\203\217\346\243\200\346\265\213", Q_NULLPTR));
        label_8->setText(QApplication::translate("ConfigDialog", "HTTP\350\266\205\346\227\266(ms):", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tabTimeout), QApplication::translate("ConfigDialog", "\350\266\205\346\227\266\350\256\276\347\275\256", Q_NULLPTR));
        autoDisplayCheck->setText(QApplication::translate("ConfigDialog", "\350\207\252\345\212\250\346\230\276\347\244\272\346\226\260\345\217\221\347\216\260\347\232\204\346\221\204\345\203\217\345\244\264", Q_NULLPTR));
        label_9->setText(QApplication::translate("ConfigDialog", "\351\273\230\350\256\244\345\210\206\345\261\217\346\225\260:", Q_NULLPTR));
        defaultModeComboBox->clear();
        defaultModeComboBox->insertItems(0, QStringList()
         << QApplication::translate("ConfigDialog", "1 (\345\215\225\347\224\273\351\235\242)", Q_NULLPTR)
         << QApplication::translate("ConfigDialog", "4 (\345\233\233\347\224\273\351\235\242)", Q_NULLPTR)
         << QApplication::translate("ConfigDialog", "9 (\344\271\235\347\224\273\351\235\242)", Q_NULLPTR)
        );
        tabWidget->setTabText(tabWidget->indexOf(tabDisplay), QApplication::translate("ConfigDialog", "\346\230\276\347\244\272\350\256\276\347\275\256", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class ConfigDialog: public Ui_ConfigDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CONFIGDIALOG_H
