#include "camerascanner.h"
#include "mythread.h"
#include "configmanager.h"
#include <QDebug>
#include <QElapsedTimer>
#include <QMetaType>
#include <QCoreApplication>
#include <QUrl>

// 注册CameraInfo为元类型，以便在信号槽中使用
static bool registerMetaTypes()
{
    qRegisterMetaType<CameraInfo>("CameraInfo");
    return true;
}

// 静态变量确保在程序启动时注册
static bool metaTypesRegistered = registerMetaTypes();

CameraScanner::CameraScanner(QObject *parent)
    : QThread(parent),
      m_isRunning(false),
      m_isUserStopped(false)
{
}

CameraScanner::~CameraScanner()
{
    stopScan();
    wait();
}

void CameraScanner::startScan()
{
    if (isRunning())
        return;

    m_isRunning = true;
    m_isUserStopped = false;
    m_results.clear();

    start();
}

void CameraScanner::stopScan()
{
    m_isRunning = false;
    m_isUserStopped = true;
}

QList<CameraInfo> CameraScanner::getResults() const
{
    QMutexLocker locker(&m_mutex);
    return m_results;
}

void CameraScanner::run()
{
    // 从配置管理器获取扫描设置
    ConfigManager &config = ConfigManager::instance();
    QString ipPrefix = config.getIpPrefix();
    int startIp = config.getStartIp();
    int endIp = config.getEndIp();
    QString username = config.getUsername();
    QString password = config.getPassword();
    QString streamPath = config.getStreamPath();
    bool useHttpDetection = config.getUseHttpDetection();
    int httpTimeout = config.getHttpTimeout();
    int rtspTimeout = config.getRtspTimeout();
    bool autoDisplay = config.getAutoDisplay();

    int total = endIp - startIp + 1;
    int current = 0;
    int foundCount = 0;

    // 检测网络连接
    qDebug() << "Starting scan of network range: " << ipPrefix << "." << startIp << " to " << ipPrefix << "." << endIp;
    qDebug() << "Using credentials: " << (username.isEmpty() ? "none" : username + ":" + password);
    qDebug() << "Stream path: " << streamPath;
    qDebug() << "RTSP timeout: " << rtspTimeout << "ms";
    qDebug() << "HTTP detection enabled: " << (useHttpDetection ? "yes" : "no");
    if (useHttpDetection)
    {
        qDebug() << "HTTP timeout: " << httpTimeout << "ms";
    }

    // 创建计时器跟踪总扫描时间
    QElapsedTimer totalTimer;
    totalTimer.start();

    for (int i = startIp; i <= endIp && m_isRunning; i++)
    {
        try
        {
            current++;

            // 创建计时器跟踪每个IP的扫描时间
            QElapsedTimer ipTimer;
            ipTimer.start();

            // 构建IP地址
            QString ip = QString("%1.%2").arg(ipPrefix).arg(i);

            // 构建RTSP URL
            QUrl url;
            url.setScheme("rtsp");
            url.setHost(ip);

            // 如果流路径不以/开头，添加/
            QString path = streamPath;
            if (!path.isEmpty() && !path.startsWith("/"))
            {
                path = "/" + path;
            }
            url.setPath(path);

            // 添加认证信息（如果有）
            if (!username.isEmpty() && !password.isEmpty())
            {
                url.setUserName(username);
                url.setPassword(password);
            }

            // 获取URL字符串
            QString rtspUrl = url.toString();

            emit scanProgress(current, total);
            qDebug().noquote() << QString("[%1/%2] Scanning IP: %3").arg(current).arg(total).arg(ip);

            // 检测变量
            bool isRtspValid = false;
            bool isHttpValid = false;

            // 单独使用计时器控制整个扫描时间
            QElapsedTimer singleIPTimer;
            singleIPTimer.start();

            // 最长扫描时间为5秒（可以根据需要调整）
            const int maxSingleIPScanTime = 5000; // 5秒

            // 先尝试HTTP图像检测（如果启用）
            if (useHttpDetection && m_isRunning && singleIPTimer.elapsed() < maxSingleIPScanTime)
            {
                try
                {
                    qDebug().noquote() << "Testing HTTP image...";
                    isHttpValid = MyThread::testHttpImage(ip, username, password, httpTimeout);
                }
                catch (...)
                {
                    qDebug() << "Exception in HTTP image test";
                    isHttpValid = false;
                }
            }

            // 如果HTTP检测失败，则测试RTSP连接
            if (!isHttpValid && m_isRunning && singleIPTimer.elapsed() < maxSingleIPScanTime)
            {
                try
                {
                    qDebug().noquote() << "Testing RTSP connection...";
                    isRtspValid = MyThread::testRtspConnection(rtspUrl, rtspTimeout);
                }
                catch (...)
                {
                    qDebug() << "Exception in RTSP connection test";
                    isRtspValid = false;
                }
            }

            // 如果扫描时间超过最大限制，强制结束当前IP的扫描
            if (singleIPTimer.elapsed() >= maxSingleIPScanTime)
            {
                qDebug() << "IP scan time exceeded maximum limit, forcing to next IP";
                isRtspValid = false;
                isHttpValid = false;
            }

            // 综合判断结果
            bool isValid = isRtspValid || isHttpValid;

            // 记录此IP扫描耗时
            qint64 ipScanTime = ipTimer.elapsed();

            // 保存结果
            CameraInfo camera(rtspUrl, ip, isValid, isHttpValid);

            // 如果有效，发出信号并添加到结果
            if (isValid)
            {
                foundCount++;
                QString detectionMethod = isHttpValid ? "HTTP image" : "RTSP stream";
                qDebug().noquote() << QString("Found valid camera [%1/%2]: %3 (detection method: %4, time: %5ms)")
                                          .arg(foundCount)
                                          .arg(current)
                                          .arg(ip)
                                          .arg(detectionMethod)
                                          .arg(ipScanTime);

                m_mutex.lock();
                m_results.append(camera);
                m_mutex.unlock();

                emit cameraFound(camera);
            }
            else
            {
                qDebug().noquote() << QString("IP address %1 has no valid camera (scan time: %2ms)")
                                          .arg(ip)
                                          .arg(ipScanTime);
            }

            // 如果这次扫描很快就完成了，给一个短暂休息，避免占用太多CPU
            if (ipScanTime < 100)
            {
                QThread::msleep(10);
            }

            // 处理Qt事件，确保没有挂起的事件
            QCoreApplication::processEvents();
        }
        catch (const std::exception &e)
        {
            qDebug() << "Exception occurred during scan: " << e.what();
            // 继续下一个IP
            continue;
        }
        catch (...)
        {
            qDebug() << "Unknown exception occurred during scan";
            // 继续下一个IP
            continue;
        }
    }

    qint64 totalTime = totalTimer.elapsed();
    qDebug() << QString("Scan completed, scanned %1 addresses, found %2 cameras, total time: %3s")
                    .arg(total)
                    .arg(foundCount)
                    .arg(totalTime / 1000.0, 0, 'f', 1);

    emit scanCompleted();
}
