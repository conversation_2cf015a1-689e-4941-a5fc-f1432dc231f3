/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../mainwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[18];
    char stringdata0[217];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 13), // "addWindowSlot"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 16), // "removeWindowSlot"
QT_MOC_LITERAL(4, 43, 9), // "aboutSlot"
QT_MOC_LITERAL(5, 53, 16), // "showConfigDialog"
QT_MOC_LITERAL(6, 70, 9), // "timerSlot"
QT_MOC_LITERAL(7, 80, 11), // "childClosed"
QT_MOC_LITERAL(8, 92, 9), // "OneVideo*"
QT_MOC_LITERAL(9, 102, 3), // "who"
QT_MOC_LITERAL(10, 106, 16), // "onCameraSelected"
QT_MOC_LITERAL(11, 123, 7), // "rtspUrl"
QT_MOC_LITERAL(12, 131, 18), // "onCameraDeselected"
QT_MOC_LITERAL(13, 150, 19), // "onCameraNameUpdated"
QT_MOC_LITERAL(14, 170, 7), // "newName"
QT_MOC_LITERAL(15, 178, 17), // "changeDisplayMode"
QT_MOC_LITERAL(16, 196, 4), // "mode"
QT_MOC_LITERAL(17, 201, 15) // "toggleMaximized"

    },
    "MainWindow\0addWindowSlot\0\0removeWindowSlot\0"
    "aboutSlot\0showConfigDialog\0timerSlot\0"
    "childClosed\0OneVideo*\0who\0onCameraSelected\0"
    "rtspUrl\0onCameraDeselected\0"
    "onCameraNameUpdated\0newName\0"
    "changeDisplayMode\0mode\0toggleMaximized"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   69,    2, 0x08 /* Private */,
       3,    0,   70,    2, 0x08 /* Private */,
       4,    0,   71,    2, 0x08 /* Private */,
       5,    0,   72,    2, 0x08 /* Private */,
       6,    0,   73,    2, 0x08 /* Private */,
       7,    1,   74,    2, 0x08 /* Private */,
      10,    1,   77,    2, 0x08 /* Private */,
      12,    1,   80,    2, 0x08 /* Private */,
      13,    2,   83,    2, 0x08 /* Private */,
      15,    1,   88,    2, 0x08 /* Private */,
      17,    0,   91,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 8,    9,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   11,   14,
    QMetaType::Void, QMetaType::Int,   16,
    QMetaType::Void,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        MainWindow *_t = static_cast<MainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->addWindowSlot(); break;
        case 1: _t->removeWindowSlot(); break;
        case 2: _t->aboutSlot(); break;
        case 3: _t->showConfigDialog(); break;
        case 4: _t->timerSlot(); break;
        case 5: _t->childClosed((*reinterpret_cast< OneVideo*(*)>(_a[1]))); break;
        case 6: _t->onCameraSelected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onCameraDeselected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->onCameraNameUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 9: _t->changeDisplayMode((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->toggleMaximized(); break;
        default: ;
        }
    }
}

const QMetaObject MainWindow::staticMetaObject = {
    { &QMainWindow::staticMetaObject, qt_meta_stringdata_MainWindow.data,
      qt_meta_data_MainWindow,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
