﻿#ifndef MYTHREAD_H
#define MYTHREAD_H

#include <QThread>
#include <QImage>
#include <QMutex>
#include <QMediaPlayer>
#include <QTcpSocket>
#include <QProcess>
#include <QWaitCondition>
#include <QAudioOutput>
#include <QBuffer>
#include <atomic> // 添加std::atomic支持

// FFmpeg头文件 - 条件编译
#ifdef USE_FFMPEG
extern "C"
{
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
#include <libswresample/swresample.h>
}
#endif

class QVideoFrame;
class QAbstractVideoSurface;

// 自定义视频表面类前向声明
class VideoSurface;

class MyThread : public QThread
{
	Q_OBJECT
public:
	explicit MyThread(QObject *parent = NULL);
	explicit MyThread(QString ip, int port, QObject *parent = NULL);
	explicit MyThread(QString rtspUrl, QObject *parent = NULL);
	~MyThread();
	void setRunning(bool running);
	QImage getLastFrame() const;					   // 获取最后一帧图像
	bool isThreadRunning() const { return isRunning; } // 添加公共方法检查线程是否在运行
	void setMuted(bool muted);						   // 设置静音状态

	// 新增: 安全停止线程，等待线程结束
	bool safeStop(int timeoutMs = 3000);

	// 测试RTSP连接是否有效
	static bool testRtspConnection(const QString &rtspUrl, int timeoutMs = 3000);

	// 测试HTTP图像是否有效
	static bool testHttpImage(const QString &ip, const QString &username = "",
							  const QString &password = "", int timeoutMs = 3000);

	// 从IP构建HTTP图像URL
	static QString buildHttpImageUrl(const QString &ip, const QString &username = "",
									 const QString &password = "");

	// 新增：使用外部ffmpeg.exe进行录像
	bool startRecordingWithFFmpegExe(const QString &outputFile);
	void stopRecordingWithFFmpegExe();

signals:
	void transmitData(QImage image);
	void disconnectSlot();

protected:
	void run() Q_DECL_OVERRIDE;
	void handleRtspConnection();
	void handleTcpConnection();
	void handleRtspWithMediaPlayer();
#ifdef USE_FFMPEG
	void handleRtspWithFFmpeg(); // 新增：使用FFmpeg处理RTSP
#endif

private:
	QString ip;
	int port;
	QString rtspUrl;
	bool isRtspMode;
	std::atomic<bool> isRunning; // 使用std::atomic<bool>替代volatile bool
	mutable QMutex mutex;		 // 使用mutable关键字允许在const方法中修改
	QMediaPlayer *mediaPlayer;
	VideoSurface *videoSurface;
	QImage lastFrame;		   // 存储最后一帧图像
	QImage lastCompleteFrame;  // 存储最后一帧完整处理的图像，用于卡顿时显示
	bool hasValidLastFrame;	   // 标记是否有有效的最后一帧
	std::atomic<bool> isMuted; // 使用std::atomic<bool>替代bool
	int consecutiveGrayFrames; // 连续检测到的灰白帧数量
	int backupFrameIndex;	   // 备用帧数组的当前索引

	// 新增: 用于线程同步
	std::atomic<bool> m_threadStopped; // 使用std::atomic<bool>替代bool

	// 新增：录像相关变量
	QProcess *recordProcess; // 录像进程

	// 辅助方法，用于清理媒体资源
	void cleanupMediaResources();

#ifdef USE_FFMPEG
	// FFmpeg相关成员
	AVFormatContext *formatContext;
	AVCodecContext *codecContext;
	AVFrame *frame;
	AVPacket *packet;
	SwsContext *swsContext;
	int videoStreamIndex;

	// 音频输出相关成员
	QAudioOutput *audioOutput;
	QIODevice *audioDevice;
	QByteArray audioBuffer;
	QBuffer *audioBufferDevice;
	SwrContext *swrContext;
	int audioOutSampleRate;
	int audioOutChannels;
	bool audioInitialized;

	// FFmpeg辅助方法
	bool initFFmpeg();
	void cleanupFFmpeg();
	bool initAudioOutput(int sampleRate, int channels, int sampleFormat);
	void cleanupAudioOutput();
	void processAudioFrame(AVFrame *frame);
#endif

private slots:
	void handleMediaPlayerError(QMediaPlayer::Error error);
	void handleMediaStatusChanged(QMediaPlayer::MediaStatus status);
	void handleSocketError(); // 添加处理Socket错误的槽函数
};

#endif // MYTHREAD_H
