# Compiled Object files
*.o
*.lo
*.slo
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.a
*.la
*.lai
*.lib

# Executables
*.exe
*.out
*.app

# cache
*~
*.bk
*.bak
*.old
*.new

# IDE
.vs
.vscode
.DS_Store

tags
cscope*
.ycm*

# output
*.pid
*.log
*.db

include
lib
bin
tmp
dist
test
*_test
build
hconfig.h
html/uploads

# msvc
*.VC.*
*.vcxproj.*
Debug
Release

# cmake
CMakeFiles
CMakeCache.txt
cmake_install.cmake
