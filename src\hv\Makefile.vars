MKDIR = -mkdir -p 2>/dev/null
CP = -cp -r 2>/dev/null
RM = -rm -r 2>/dev/null

INSTALL_INCDIR=/usr/local/include/hv
INSTALL_LIBDIR=/usr/local/lib

BASE_HEADERS =  base/hplatform.h\
				\
				base/hdef.h\
				base/hatomic.h\
				base/herr.h\
				base/htime.h\
				base/hmath.h\
				base/hbase.h\
				base/hversion.h\
				base/hsysinfo.h\
				base/hproc.h\
				base/hthread.h\
				base/hmutex.h\
				base/hsocket.h\
				base/hssl.h\
				base/hlog.h\
				base/hbuf.h\
				\
				base/hmap.h\
				base/hstring.h\
				base/hfile.h\
				base/hdir.h\
				base/hurl.h\
				base/hscope.h\
				base/hthreadpool.h\
				base/hobjectpool.h\
				base/ifconfig.h\
				base/ThreadLocalStorage.h\

UTILS_HEADERS = utils/base64.h\
				utils/md5.h\
				utils/sha1.h\
				utils/json.hpp\
				utils/singleton.h\
				utils/iniparser.h\
				utils/hendian.h\
				utils/hmain.h\

EVENT_HEADERS = event/hloop.h\
				event/nlog.h\
				event/nmap.h\

EVPP_HEADERS  = evpp/Buffer.h\
				evpp/Callback.h\
				evpp/Channel.h\
				evpp/Event.h\
				evpp/EventLoop.h\
				evpp/EventLoopThread.h\
				evpp/EventLoopThreadPool.h\
				evpp/Status.h\
				evpp/TcpClient.h\
				evpp/TcpServer.h\
				evpp/UdpClient.h\
				evpp/UdpServer.h\

PROTOCOL_HEADERS =  protocol/icmp.h\
					protocol/dns.h\
					protocol/ftp.h\
					protocol/smtp.h

HTTP_HEADERS =  http/httpdef.h\
				http/http2def.h\
				http/grpcdef.h\
				http/wsdef.h\
				http/http_content.h\
				http/HttpMessage.h\
				http/HttpParser.h\
				http/WebSocketParser.h\
				http/WebSocketChannel.h\

HTTP_CLIENT_HEADERS =   http/client/http_client.h\
						http/client/requests.h\
						http/client/WebSocketClient.h\

HTTP_SERVER_HEADERS =   http/server/HttpService.h\
						http/server/HttpServer.h\
						http/server/WebSocketServer.h\

CONSUL_HEADERS = consul/consul.h
