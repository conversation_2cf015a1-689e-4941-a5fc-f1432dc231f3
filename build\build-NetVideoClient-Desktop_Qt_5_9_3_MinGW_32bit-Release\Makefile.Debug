#############################################################################
# Makefile for building: CameraVideo
# Generated by qmake (3.1) (Qt 5.9.3)
# Project:  ..\..\NetVideoClient.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DUSE_FFMPEG -D__STDC_CONSTANT_MACROS -DQT_MULTIMEDIAWIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_SQL_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wextra -Wall -W $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++11 -Wextra -Wall -W -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\..\LiveCamera -I. -I..\..\ffmpeg\include -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtMultimediaWidgets -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtMultimedia -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtWidgets -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtGui -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtANGLE -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtNetwork -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtSql -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\include\QtCore -Idebug -I. -IC:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lmingw32 -LC:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libqtmaind.a -LC:\utils\my_sql\my_sql\lib -LC:\utils\postgresql\pgsql\lib -lshell32 -LD:\study\new\LiveCamera\ffmpeg\lib -lavformat -lavcodec -lavutil -lswscale -lswresample -lws2_32 -Ldebug -lsqlite3 -lzf_KeyCheck C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5MultimediaWidgetsd.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Multimediad.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Widgetsd.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Guid.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Networkd.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Sqld.a C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\libQt5Cored.a debug\CameraVideo_resource_res.o 
QMAKE         = C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = debug\CameraVideo_resource_res.o
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i
QINSTALL        = C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\qmake.exe -install qinstall -exe

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\onevideo.cpp \
		..\..\mythread.cpp \
		..\..\configdialog.cpp \
		..\..\fullscreenvideo.cpp \
		..\..\camerascanner.cpp \
		..\..\camerascanwidget.cpp \
		..\..\videosurface.cpp \
		..\..\configmanager.cpp \
		..\..\iconbutton.cpp \
		..\..\singleapplication.cpp \
		..\..\logmanager.cpp debug\qrc_res.cpp \
		debug\moc_mainwindow.cpp \
		debug\moc_onevideo.cpp \
		debug\moc_mythread.cpp \
		debug\moc_configdialog.cpp \
		debug\moc_fullscreenvideo.cpp \
		debug\moc_camerascanner.cpp \
		debug\moc_camerascanwidget.cpp \
		debug\moc_videosurface.cpp \
		debug\moc_configmanager.cpp \
		debug\moc_iconbutton.cpp \
		debug\moc_singleapplication.cpp \
		debug\moc_globalsignal.cpp \
		debug\moc_logmanager.cpp
OBJECTS       = debug/main.o \
		debug/mainwindow.o \
		debug/onevideo.o \
		debug/mythread.o \
		debug/configdialog.o \
		debug/fullscreenvideo.o \
		debug/camerascanner.o \
		debug/camerascanwidget.o \
		debug/videosurface.o \
		debug/configmanager.o \
		debug/iconbutton.o \
		debug/singleapplication.o \
		debug/logmanager.o \
		debug/qrc_res.o \
		debug/moc_mainwindow.o \
		debug/moc_onevideo.o \
		debug/moc_mythread.o \
		debug/moc_configdialog.o \
		debug/moc_fullscreenvideo.o \
		debug/moc_camerascanner.o \
		debug/moc_camerascanwidget.o \
		debug/moc_videosurface.o \
		debug/moc_configmanager.o \
		debug/moc_iconbutton.o \
		debug/moc_singleapplication.o \
		debug/moc_globalsignal.o \
		debug/moc_logmanager.o

DIST          =  ..\..\mainwindow.h \
		..\..\onevideo.h \
		..\..\mythread.h \
		..\..\configdialog.h \
		..\..\fullscreenvideo.h \
		..\..\camerascanner.h \
		..\..\camerascanwidget.h \
		..\..\videosurface.h \
		..\..\configmanager.h \
		..\..\iconbutton.h \
		..\..\singleapplication.h \
		..\..\zf_UdiskAndSoftKeyCheck.h \
		..\..\globalsignal.h \
		..\..\logmanager.h ..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\onevideo.cpp \
		..\..\mythread.cpp \
		..\..\configdialog.cpp \
		..\..\fullscreenvideo.cpp \
		..\..\camerascanner.cpp \
		..\..\camerascanwidget.cpp \
		..\..\videosurface.cpp \
		..\..\configmanager.cpp \
		..\..\iconbutton.cpp \
		..\..\singleapplication.cpp \
		..\..\logmanager.cpp
QMAKE_TARGET  = CameraVideo
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = CameraVideo.exe
DESTDIR_TARGET = debug\CameraVideo.exe

####### Build rules

first: all
all: Makefile.Debug  $(DESTDIR_TARGET)

$(DESTDIR_TARGET): ui_configdialog.h $(OBJECTS) debug/CameraVideo_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) object_script.CameraVideo.Debug  $(LIBS)
	copy /y "D:/study/new/LiveCamera/ffmpeg/bin\avformat-59.dll" "D:/study/new/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\"
	 copy /y "D:/study/new/LiveCamera/ffmpeg/bin\avcodec-59.dll" "D:/study/new/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\"
	 copy /y "D:/study/new/LiveCamera/ffmpeg/bin\avutil-57.dll" "D:/study/new/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\"
	 copy /y "D:/study/new/LiveCamera/ffmpeg/bin\swscale-6.dll" "D:/study/new/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\"
	 copy /y "D:/study/new/LiveCamera/ffmpeg/bin\swresample-4.dll" "D:/study/new/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\"
	

debug/CameraVideo_resource_res.o: CameraVideo_resource.rc
	windres -i CameraVideo_resource.rc -o debug\CameraVideo_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\NetVideoClient.pro -spec win32-g++

qmake_all: FORCE

dist:
	$(ZIP) CameraVideo.zip $(SOURCES) $(DIST) ..\..\..\..\NetVideoClient.pro C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\spec_pre.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\qdevice.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\device_config.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\common\g++-base.conf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\common\angle.conf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\common\sanitize.conf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\common\gcc-base.conf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\qconfig.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3danimation.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3danimation_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dcore.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dcore_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dextras.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dextras_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dinput.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dinput_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dlogic.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dlogic_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquick.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquick_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickanimation.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickanimation_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickextras.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickextras_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickinput.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickinput_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickrender.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickrender_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickscene2d.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3dquickscene2d_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3drender.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_3drender_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_accessibility_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axbase.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axbase_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axcontainer.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axcontainer_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axserver.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_axserver_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_bluetooth.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_bluetooth_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_bootstrap_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_charts.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_charts_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_concurrent.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_concurrent_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_core.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_core_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_datavisualization.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_datavisualization_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_dbus.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_dbus_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_designer.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_designer_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_designercomponents_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_egl_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_fb_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_fontdatabase_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_gamepad.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_gamepad_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_gui.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_gui_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_help.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_help_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_location.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_location_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_multimedia.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_multimedia_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_multimediawidgets.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_network.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_network_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_networkauth.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_networkauth_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_nfc.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_nfc_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_opengl.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_opengl_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_openglextensions.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_openglextensions_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_platformcompositor_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_positioning.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_positioning_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_printsupport.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_printsupport_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_purchasing.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_purchasing_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qml.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qml_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qmldebug_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qmldevtools_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qmltest.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qmltest_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quick.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quick_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quickcontrols2.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quickparticles_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quickwidgets.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_remoteobjects.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_remoteobjects_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_repparser.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_repparser_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_script.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_script_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_scripttools.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_scripttools_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_scxml.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_scxml_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_sensors.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_sensors_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_serialbus.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_serialbus_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_serialport.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_serialport_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_sql.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_sql_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_svg.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_svg_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_testlib.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_testlib_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_texttospeech.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_texttospeech_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_theme_support_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_uiplugin.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_uitools.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_uitools_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_webchannel.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_webchannel_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_websockets.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_websockets_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_widgets.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_widgets_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_winextras.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_winextras_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_xml.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_xml_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_xmlpatterns.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\qt_functions.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\qt_config.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\win32-g++\qmake.conf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\spec_post.prf .qmake.stash C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\exclusive_builds.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\toolchain.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\default_pre.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\win32\default_pre.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\resolve_config.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\exclusive_builds_post.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\default_post.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\build_pass.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\precompile_header.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\warn_on.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\qt.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\resources.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\moc.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\win32\opengl.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\uic.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\qmake_use.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\file_copies.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\win32\windows.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\testcase_targets.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\exceptions.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\yacc.prf C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\lex.prf ..\..\NetVideoClient.pro ..\..\res.qrc C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\qtmaind.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5MultimediaWidgetsd.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Multimediad.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Widgetsd.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Guid.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Networkd.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Sqld.prl C:\Qt\Qt5.9.3\5.9.3\mingw53_32\lib\Qt5Cored.prl   ..\..\res.qrc C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\data\dummy.cpp ..\..\mainwindow.h ..\..\onevideo.h ..\..\mythread.h ..\..\configdialog.h ..\..\fullscreenvideo.h ..\..\camerascanner.h ..\..\camerascanwidget.h ..\..\videosurface.h ..\..\configmanager.h ..\..\iconbutton.h ..\..\singleapplication.h ..\..\zf_UdiskAndSoftKeyCheck.h ..\..\globalsignal.h ..\..\logmanager.h ..\..\main.cpp ..\..\mainwindow.cpp ..\..\onevideo.cpp ..\..\mythread.cpp ..\..\configdialog.cpp ..\..\fullscreenvideo.cpp ..\..\camerascanner.cpp ..\..\camerascanwidget.cpp ..\..\videosurface.cpp ..\..\configmanager.cpp ..\..\iconbutton.cpp ..\..\singleapplication.cpp ..\..\logmanager.cpp ..\..\configdialog.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\mainwindow.o debug\onevideo.o debug\mythread.o debug\configdialog.o debug\fullscreenvideo.o debug\camerascanner.o debug\camerascanwidget.o debug\videosurface.o debug\configmanager.o debug\iconbutton.o debug\singleapplication.o debug\logmanager.o debug\qrc_res.o debug\moc_mainwindow.o debug\moc_onevideo.o debug\moc_mythread.o debug\moc_configdialog.o debug\moc_fullscreenvideo.o debug\moc_camerascanner.o debug\moc_camerascanwidget.o debug\moc_videosurface.o debug\moc_configmanager.o debug\moc_iconbutton.o debug\moc_singleapplication.o debug\moc_globalsignal.o debug\moc_logmanager.o
	-$(DEL_FILE) debug\CameraVideo_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_res.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_res.cpp
debug/qrc_res.cpp: ../../res.qrc \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/rcc.exe \
		../../images/record.png \
		../../images/mute.png \
		../../images/app.ico \
		../../images/background.png \
		../../images/pause.png \
		../../images/close.png \
		../../images/list_bg.png \
		../../images/4_def.png \
		../../images/play.png \
		../../images/banner.png \
		../../images/camera.png \
		../../images/monitor_bg.png \
		../../images/fullscreen.png \
		../../images/rotate.png \
		../../images/9_def.png \
		../../images/unmute.png \
		../../images/1.png \
		../../images/1_def.png \
		../../images/4.png \
		../../images/9.png
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\rcc.exe -name res ..\..\res.qrc -o debug\qrc_res.cpp

compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++11 -Wextra -Wall -W -dM -E -o debug\moc_predefs.h C:\Qt\Qt5.9.3\5.9.3\mingw53_32\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_mainwindow.cpp debug/moc_onevideo.cpp debug/moc_mythread.cpp debug/moc_configdialog.cpp debug/moc_fullscreenvideo.cpp debug/moc_camerascanner.cpp debug/moc_camerascanwidget.cpp debug/moc_videosurface.cpp debug/moc_configmanager.cpp debug/moc_iconbutton.cpp debug/moc_singleapplication.cpp debug/moc_globalsignal.cpp debug/moc_logmanager.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_mainwindow.cpp debug\moc_onevideo.cpp debug\moc_mythread.cpp debug\moc_configdialog.cpp debug\moc_fullscreenvideo.cpp debug\moc_camerascanner.cpp debug\moc_camerascanwidget.cpp debug\moc_videosurface.cpp debug\moc_configmanager.cpp debug\moc_iconbutton.cpp debug\moc_singleapplication.cpp debug\moc_globalsignal.cpp debug\moc_logmanager.cpp
debug/moc_mainwindow.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QSplitter \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsplitter.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMap \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QPoint \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QToolButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtoolbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QButtonGroup \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qbuttongroup.h \
		../../mainwindow.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\mainwindow.h -o debug\moc_mainwindow.cpp

debug/moc_onevideo.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaRecorder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediarecorder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaencodersettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediabindableinterface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		../../onevideo.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\onevideo.h -o debug\moc_onevideo.cpp

debug/moc_mythread.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaPlayer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaplayer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediacontent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaresource.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QUrl \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudio.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkconfiguration.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QTcpSocket \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAudioOutput \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiooutput.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudioformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiodeviceinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QBuffer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbuffer.h \
		../../ffmpeg/include/libavformat/avformat.h \
		../../ffmpeg/include/libavcodec/codec.h \
		../../ffmpeg/include/libavutil/avutil.h \
		../../ffmpeg/include/libavutil/common.h \
		../../ffmpeg/include/libavutil/attributes.h \
		../../ffmpeg/include/libavutil/macros.h \
		../../ffmpeg/include/libavutil/avconfig.h \
		../../ffmpeg/include/libavutil/mem.h \
		../../ffmpeg/include/libavutil/version.h \
		../../ffmpeg/include/libavutil/error.h \
		../../ffmpeg/include/libavutil/rational.h \
		../../ffmpeg/include/libavutil/mathematics.h \
		../../ffmpeg/include/libavutil/intfloat.h \
		../../ffmpeg/include/libavutil/log.h \
		../../ffmpeg/include/libavutil/pixfmt.h \
		../../ffmpeg/include/libavutil/hwcontext.h \
		../../ffmpeg/include/libavutil/buffer.h \
		../../ffmpeg/include/libavutil/frame.h \
		../../ffmpeg/include/libavutil/channel_layout.h \
		../../ffmpeg/include/libavutil/dict.h \
		../../ffmpeg/include/libavutil/samplefmt.h \
		../../ffmpeg/include/libavcodec/codec_id.h \
		../../ffmpeg/include/libavcodec/version_major.h \
		../../ffmpeg/include/libavcodec/codec_par.h \
		../../ffmpeg/include/libavcodec/defs.h \
		../../ffmpeg/include/libavcodec/packet.h \
		../../ffmpeg/include/libavformat/avio.h \
		../../ffmpeg/include/libavformat/version_major.h \
		../../ffmpeg/include/libavformat/version.h \
		../../ffmpeg/include/libavcodec/avcodec.h \
		../../ffmpeg/include/libavcodec/codec_desc.h \
		../../ffmpeg/include/libswscale/swscale.h \
		../../ffmpeg/include/libswscale/version_major.h \
		../../ffmpeg/include/libavutil/imgutils.h \
		../../ffmpeg/include/libavutil/pixdesc.h \
		../../ffmpeg/include/libswresample/swresample.h \
		../../ffmpeg/include/libswresample/version_major.h \
		../../ffmpeg/include/libswresample/version.h \
		../../mythread.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\mythread.h -o debug\moc_mythread.cpp

debug/moc_configdialog.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		../../configdialog.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\configdialog.h -o debug\moc_configdialog.cpp

debug/moc_fullscreenvideo.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		../../fullscreenvideo.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\fullscreenvideo.h -o debug\moc_fullscreenvideo.cpp

debug/moc_camerascanner.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../camerascanner.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\camerascanner.h -o debug\moc_camerascanner.cpp

debug/moc_camerascanwidget.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QTableWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtablewidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtableview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qabstractitemmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qitemselectionmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyleoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyle.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabbar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qrubberband.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMenu \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmenu.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qaction.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qactiongroup.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHBoxLayout \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../camerascanwidget.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\camerascanwidget.h -o debug\moc_camerascanwidget.cpp

debug/moc_videosurface.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAbstractVideoSurface \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideosurface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideoframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideobuffer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoSurfaceFormat \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideosurfaceformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		../../videosurface.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\videosurface.h -o debug\moc_videosurface.cpp

debug/moc_configmanager.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../configmanager.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\configmanager.h -o debug\moc_configmanager.cpp

debug/moc_iconbutton.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		../../iconbutton.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\iconbutton.h -o debug\moc_iconbutton.cpp

debug/moc_singleapplication.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qinputmethod.h \
		../../singleapplication.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\singleapplication.h -o debug\moc_singleapplication.cpp

debug/moc_globalsignal.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		../../singleton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutexLocker \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QScopedPointer \
		../../globalsignal.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\globalsignal.h -o debug\moc_globalsignal.cpp

debug/moc_logmanager.cpp: C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		../../logmanager.h \
		debug/moc_predefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/moc.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\moc.exe $(DEFINES) --include debug/moc_predefs.h -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/mkspecs/win32-g++ -ID:/study/new/LiveCamera -ID:/study/new/LiveCamera/ffmpeg/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimediaWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtANGLE -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql -IC:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore -I. -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/lib/gcc/i686-w64-mingw32/5.3.0/include-fixed -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++ -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Qt/Qt5.9.3/Tools/mingw530_32/i686-w64-mingw32/include/c++/backward ..\..\logmanager.h -o debug\moc_logmanager.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_configdialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_configdialog.h
ui_configdialog.h: ../../configdialog.ui \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/bin/uic.exe
	C:\Qt\Qt5.9.3\5.9.3\mingw53_32\bin\uic.exe ..\..\configdialog.ui -o ui_configdialog.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: ../../main.cpp ../../mainwindow.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QSplitter \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsplitter.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMap \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QPoint \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QToolButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtoolbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QButtonGroup \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qbuttongroup.h \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextCodec \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextcodec.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFileInfo \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		../../SingleApplication.h \
		../../zf_UdiskAndSoftKeyCheck.h \
		../../GlobalSignal.h \
		../../singleton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutexLocker \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QScopedPointer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDir \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdir.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QScreen \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qscreen.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QRect \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSize \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSizeF \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QTransform \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTranslator \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtranslator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QLibrary \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlibrary.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\main.cpp

debug/mainwindow.o: ../../mainwindow.cpp ../../mainwindow.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QSplitter \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsplitter.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMap \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QPoint \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QToolButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtoolbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QButtonGroup \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qbuttongroup.h \
		../../onevideo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaRecorder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediarecorder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaencodersettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediabindableinterface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		../../camerascanwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QTableWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtablewidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtableview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qabstractitemmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qitemselectionmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyleoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyle.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabbar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qrubberband.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMenu \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmenu.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qaction.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qactiongroup.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHBoxLayout \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../configdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMenuBar \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmenubar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QAction \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QStatusBar \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstatusbar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QScrollArea \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qscrollarea.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QGridLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QStackedLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstackedlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QMouseEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QScreen \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qscreen.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QRect \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSize \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSizeF \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QTransform \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QResizeEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\mainwindow.o ..\..\mainwindow.cpp

debug/onevideo.o: ../../onevideo.cpp ../../onevideo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaRecorder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediarecorder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaencodersettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediabindableinterface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		../../iconbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		../../mythread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaPlayer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaplayer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediacontent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaresource.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QUrl \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudio.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkconfiguration.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QTcpSocket \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAudioOutput \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiooutput.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudioformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiodeviceinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QBuffer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbuffer.h \
		../../ffmpeg/include/libavformat/avformat.h \
		../../ffmpeg/include/libavcodec/codec.h \
		../../ffmpeg/include/libavutil/avutil.h \
		../../ffmpeg/include/libavutil/common.h \
		../../ffmpeg/include/libavutil/attributes.h \
		../../ffmpeg/include/libavutil/macros.h \
		../../ffmpeg/include/libavutil/avconfig.h \
		../../ffmpeg/include/libavutil/mem.h \
		../../ffmpeg/include/libavutil/version.h \
		../../ffmpeg/include/libavutil/error.h \
		../../ffmpeg/include/libavutil/rational.h \
		../../ffmpeg/include/libavutil/mathematics.h \
		../../ffmpeg/include/libavutil/intfloat.h \
		../../ffmpeg/include/libavutil/log.h \
		../../ffmpeg/include/libavutil/pixfmt.h \
		../../ffmpeg/include/libavutil/hwcontext.h \
		../../ffmpeg/include/libavutil/buffer.h \
		../../ffmpeg/include/libavutil/frame.h \
		../../ffmpeg/include/libavutil/channel_layout.h \
		../../ffmpeg/include/libavutil/dict.h \
		../../ffmpeg/include/libavutil/samplefmt.h \
		../../ffmpeg/include/libavcodec/codec_id.h \
		../../ffmpeg/include/libavcodec/version_major.h \
		../../ffmpeg/include/libavcodec/codec_par.h \
		../../ffmpeg/include/libavcodec/defs.h \
		../../ffmpeg/include/libavcodec/packet.h \
		../../ffmpeg/include/libavformat/avio.h \
		../../ffmpeg/include/libavformat/version_major.h \
		../../ffmpeg/include/libavformat/version.h \
		../../ffmpeg/include/libavcodec/avcodec.h \
		../../ffmpeg/include/libavcodec/codec_desc.h \
		../../ffmpeg/include/libswscale/swscale.h \
		../../ffmpeg/include/libswscale/version_major.h \
		../../ffmpeg/include/libavutil/imgutils.h \
		../../ffmpeg/include/libavutil/pixdesc.h \
		../../ffmpeg/include/libswresample/swresample.h \
		../../ffmpeg/include/libswresample/version_major.h \
		../../ffmpeg/include/libswresample/version.h \
		../../configdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		../../fullscreenvideo.h \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		../../camerascanner.h \
		../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QPainter \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainter.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpen.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QLinearGradient \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QPaintEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QMouseEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QTransform \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QResizeEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QPen \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDir \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdir.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStandardPaths \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstandardpaths.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideoframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideobuffer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QCamera \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qcamera.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediacontrol.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaservice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qcameraexposure.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qcamerafocus.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qcameraimageprocessing.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qcameraviewfindersettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoEncoderSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaService \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaContent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFileInfo \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\onevideo.o ..\..\onevideo.cpp

debug/mythread.o: ../../mythread.cpp ../../mythread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaPlayer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaplayer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediacontent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaresource.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QUrl \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudio.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkconfiguration.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QTcpSocket \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAudioOutput \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiooutput.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudioformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiodeviceinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QBuffer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbuffer.h \
		../../ffmpeg/include/libavformat/avformat.h \
		../../ffmpeg/include/libavcodec/codec.h \
		../../ffmpeg/include/libavutil/avutil.h \
		../../ffmpeg/include/libavutil/common.h \
		../../ffmpeg/include/libavutil/attributes.h \
		../../ffmpeg/include/libavutil/macros.h \
		../../ffmpeg/include/libavutil/avconfig.h \
		../../ffmpeg/include/libavutil/mem.h \
		../../ffmpeg/include/libavutil/version.h \
		../../ffmpeg/include/libavutil/error.h \
		../../ffmpeg/include/libavutil/rational.h \
		../../ffmpeg/include/libavutil/mathematics.h \
		../../ffmpeg/include/libavutil/intfloat.h \
		../../ffmpeg/include/libavutil/log.h \
		../../ffmpeg/include/libavutil/pixfmt.h \
		../../ffmpeg/include/libavutil/hwcontext.h \
		../../ffmpeg/include/libavutil/buffer.h \
		../../ffmpeg/include/libavutil/frame.h \
		../../ffmpeg/include/libavutil/channel_layout.h \
		../../ffmpeg/include/libavutil/dict.h \
		../../ffmpeg/include/libavutil/samplefmt.h \
		../../ffmpeg/include/libavcodec/codec_id.h \
		../../ffmpeg/include/libavcodec/version_major.h \
		../../ffmpeg/include/libavcodec/codec_par.h \
		../../ffmpeg/include/libavcodec/defs.h \
		../../ffmpeg/include/libavcodec/packet.h \
		../../ffmpeg/include/libavformat/avio.h \
		../../ffmpeg/include/libavformat/version_major.h \
		../../ffmpeg/include/libavformat/version.h \
		../../ffmpeg/include/libavcodec/avcodec.h \
		../../ffmpeg/include/libavcodec/codec_desc.h \
		../../ffmpeg/include/libswscale/swscale.h \
		../../ffmpeg/include/libswscale/version_major.h \
		../../ffmpeg/include/libavutil/imgutils.h \
		../../ffmpeg/include/libavutil/pixdesc.h \
		../../ffmpeg/include/libswresample/swresample.h \
		../../ffmpeg/include/libswresample/version_major.h \
		../../ffmpeg/include/libswresample/version.h \
		../../videosurface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAbstractVideoSurface \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideosurface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideoframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideobuffer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoSurfaceFormat \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideosurfaceformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QNetworkAccessManager \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkaccessmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVector \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFlags \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMetaType \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QNetworkReply \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkreply.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QIODevice \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QEventLoop \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QRegExp \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTemporaryFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtemporaryfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDir \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdir.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutexLocker \
		../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		../../ffmpeg/include/libavutil/opt.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\mythread.o ..\..\mythread.cpp

debug/configdialog.o: ../../configdialog.cpp ../../configdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		ui_configdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QAction \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qaction.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qactiongroup.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QButtonGroup \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qbuttongroup.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QCheckBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qcheckbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QComboBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qcombobox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyleoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyle.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabbar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qrubberband.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qabstractitemmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialogButtonBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialogbuttonbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QFormLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qformlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHeaderView \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qheaderview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qitemselectionmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLineEdit \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlineedit.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpen.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QSpacerItem \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QSpinBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qspinbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QTabWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QWidget \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\configdialog.o ..\..\configdialog.cpp

debug/fullscreenvideo.o: ../../fullscreenvideo.cpp ../../fullscreenvideo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QPaintEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QPainter \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainter.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpen.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\fullscreenvideo.o ..\..\fullscreenvideo.cpp

debug/camerascanner.o: ../../camerascanner.cpp ../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../mythread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QMediaPlayer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaplayer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediacontent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaresource.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QUrl \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmediaenumdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudio.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qnetworkconfiguration.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QTcpSocket \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QProcess \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocess.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAudioOutput \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiooutput.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudioformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qaudiodeviceinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QBuffer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbuffer.h \
		../../ffmpeg/include/libavformat/avformat.h \
		../../ffmpeg/include/libavcodec/codec.h \
		../../ffmpeg/include/libavutil/avutil.h \
		../../ffmpeg/include/libavutil/common.h \
		../../ffmpeg/include/libavutil/attributes.h \
		../../ffmpeg/include/libavutil/macros.h \
		../../ffmpeg/include/libavutil/avconfig.h \
		../../ffmpeg/include/libavutil/mem.h \
		../../ffmpeg/include/libavutil/version.h \
		../../ffmpeg/include/libavutil/error.h \
		../../ffmpeg/include/libavutil/rational.h \
		../../ffmpeg/include/libavutil/mathematics.h \
		../../ffmpeg/include/libavutil/intfloat.h \
		../../ffmpeg/include/libavutil/log.h \
		../../ffmpeg/include/libavutil/pixfmt.h \
		../../ffmpeg/include/libavutil/hwcontext.h \
		../../ffmpeg/include/libavutil/buffer.h \
		../../ffmpeg/include/libavutil/frame.h \
		../../ffmpeg/include/libavutil/channel_layout.h \
		../../ffmpeg/include/libavutil/dict.h \
		../../ffmpeg/include/libavutil/samplefmt.h \
		../../ffmpeg/include/libavcodec/codec_id.h \
		../../ffmpeg/include/libavcodec/version_major.h \
		../../ffmpeg/include/libavcodec/codec_par.h \
		../../ffmpeg/include/libavcodec/defs.h \
		../../ffmpeg/include/libavcodec/packet.h \
		../../ffmpeg/include/libavformat/avio.h \
		../../ffmpeg/include/libavformat/version_major.h \
		../../ffmpeg/include/libavformat/version.h \
		../../ffmpeg/include/libavcodec/avcodec.h \
		../../ffmpeg/include/libavcodec/codec_desc.h \
		../../ffmpeg/include/libswscale/swscale.h \
		../../ffmpeg/include/libswscale/version_major.h \
		../../ffmpeg/include/libavutil/imgutils.h \
		../../ffmpeg/include/libavutil/pixdesc.h \
		../../ffmpeg/include/libswresample/swresample.h \
		../../ffmpeg/include/libswresample/version_major.h \
		../../ffmpeg/include/libswresample/version.h \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMetaType \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\camerascanner.o ..\..\camerascanner.cpp

debug/camerascanwidget.o: ../../camerascanwidget.cpp ../../camerascanwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLabel \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QTableWidget \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtablewidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtableview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qabstractitemmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qitemselectionmodel.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyleoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qstyle.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabbar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qrubberband.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMenu \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmenu.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qaction.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qactiongroup.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTimer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHBoxLayout \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QIcon \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QAction \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QHeaderView \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qheaderview.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QCheckBox \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qcheckbox.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QResizeEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QBrush \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QColor \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QTableWidgetItem \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QInputDialog \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qinputdialog.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qlineedit.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpen.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QLineEdit \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutexLocker \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QFont \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QEvent
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\camerascanwidget.o ..\..\camerascanwidget.cpp

debug/videosurface.o: ../../videosurface.cpp ../../videosurface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QAbstractVideoSurface \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideosurface.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideoframe.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qabstractvideobuffer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimediaglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qtmultimedia-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qmultimedia.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoSurfaceFormat \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/qvideosurfaceformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtMultimedia/QVideoFrame \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QImage \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\videosurface.o ..\..\videosurface.cpp

debug/configmanager.o: ../../configmanager.cpp ../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDir \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdir.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlQuery \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlError \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqlerror.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextCodec \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextcodec.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\configmanager.o ..\..\configmanager.cpp

debug/iconbutton.o: ../../iconbutton.cpp ../../iconbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QPushButton \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qpushbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qabstractbutton.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qicon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QMouseEvent \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\iconbutton.o ..\..\iconbutton.cpp

debug/singleapplication.o: ../../singleapplication.cpp C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QLocalSocket \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qlocalsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/QLocalServer \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtNetwork/qlocalserver.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		../../singleapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/QApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcursor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpalette.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qcolor.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgb.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qbrush.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qline.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtransform.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qimage.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfont.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\singleapplication.o ..\..\singleapplication.cpp

debug/logmanager.o: ../../logmanager.cpp ../../logmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QObject \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qconfig.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlogging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qflags.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmutex.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstring.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qchar.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiterator.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpair.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qregexp.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFile \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfile.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QTextStream \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qlocale.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvariant.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmap.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdebug.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qhash.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qvector.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qpoint.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qset.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutex \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDateTime \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QString \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStringBuilder \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QThread \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qthread.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QByteArray \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QVariant \
		../../configmanager.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QSettings \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsettings.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/QSqlDatabase \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qsqldatabase.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtSql/qtsqlglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/QList \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qevent.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qregion.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qrect.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qmargins.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qsize.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurl.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtGui/qtouchdevice.h \
		../../camerascanner.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDir \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qdir.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QMutexLocker \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QDebug \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QStandardPaths \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/qstandardpaths.h \
		C:/Qt/Qt5.9.3/5.9.3/mingw53_32/include/QtCore/QFileInfo
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\logmanager.o ..\..\logmanager.cpp

debug/qrc_res.o: debug/qrc_res.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_res.o debug\qrc_res.cpp

debug/moc_mainwindow.o: debug/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_mainwindow.o debug\moc_mainwindow.cpp

debug/moc_onevideo.o: debug/moc_onevideo.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_onevideo.o debug\moc_onevideo.cpp

debug/moc_mythread.o: debug/moc_mythread.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_mythread.o debug\moc_mythread.cpp

debug/moc_configdialog.o: debug/moc_configdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_configdialog.o debug\moc_configdialog.cpp

debug/moc_fullscreenvideo.o: debug/moc_fullscreenvideo.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_fullscreenvideo.o debug\moc_fullscreenvideo.cpp

debug/moc_camerascanner.o: debug/moc_camerascanner.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_camerascanner.o debug\moc_camerascanner.cpp

debug/moc_camerascanwidget.o: debug/moc_camerascanwidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_camerascanwidget.o debug\moc_camerascanwidget.cpp

debug/moc_videosurface.o: debug/moc_videosurface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_videosurface.o debug\moc_videosurface.cpp

debug/moc_configmanager.o: debug/moc_configmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_configmanager.o debug\moc_configmanager.cpp

debug/moc_iconbutton.o: debug/moc_iconbutton.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_iconbutton.o debug\moc_iconbutton.cpp

debug/moc_singleapplication.o: debug/moc_singleapplication.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_singleapplication.o debug\moc_singleapplication.cpp

debug/moc_globalsignal.o: debug/moc_globalsignal.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_globalsignal.o debug\moc_globalsignal.cpp

debug/moc_logmanager.o: debug/moc_logmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_logmanager.o debug\moc_logmanager.cpp

####### Install

install_ffmpeg_dlls: first FORCE
	@if not exist D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug mkdir D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug & if not exist D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug exit 1
	-$(QINSTALL) D:\study\new\LiveCamera\ffmpeg\bin\avformat-59.dll D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avformat-59.dll
	-$(QINSTALL) D:\study\new\LiveCamera\ffmpeg\bin\avcodec-59.dll D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avcodec-59.dll
	-$(QINSTALL) D:\study\new\LiveCamera\ffmpeg\bin\avutil-57.dll D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avutil-57.dll
	-$(QINSTALL) D:\study\new\LiveCamera\ffmpeg\bin\swscale-6.dll D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\swscale-6.dll
	-$(QINSTALL) D:\study\new\LiveCamera\ffmpeg\bin\swresample-4.dll D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\swresample-4.dll

uninstall_ffmpeg_dlls: FORCE
	-$(DEL_FILE) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\swresample-4.dll
	-$(DEL_FILE) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\swscale-6.dll
	-$(DEL_FILE) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avutil-57.dll
	-$(DEL_FILE) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avcodec-59.dll
	-$(DEL_FILE) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug\avformat-59.dll
	-$(DEL_DIR) D:$(INSTALL_ROOT:@msyshack@%=%)\study\new\LiveCamera\build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\debug 


install: install_ffmpeg_dlls  FORCE

uninstall: uninstall_ffmpeg_dlls  FORCE

FORCE:

