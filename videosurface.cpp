#include "videosurface.h"
#include <QVideoFrame>
#include <QDebug>
#include <QThread>

VideoSurface::VideoSurface(QObject *parent) : QAbstractVideoSurface(parent)
{
    qDebug() << "VideoSurface create in thread:" << QThread::currentThread();
}

VideoSurface::~VideoSurface()
{
    qDebug() << "VideoSurface destroy in thread:" << QThread::currentThread();
}

QList<QVideoFrame::PixelFormat> VideoSurface::supportedPixelFormats(
    QAbstractVideoBuffer::HandleType handleType) const
{
    // 支持的像素格式列表
    return QList<QVideoFrame::PixelFormat>()
           << QVideoFrame::Format_RGB32
           << QVideoFrame::Format_ARGB32
           << QVideoFrame::Format_ARGB32_Premultiplied
           << QVideoFrame::Format_RGB24
           << QVideoFrame::Format_RGB565
           << QVideoFrame::Format_RGB555;
}

bool VideoSurface::present(const QVideoFrame &frame)
{
    if (frame.isValid())
    {
        QVideoFrame cloneFrame(frame);
        if (cloneFrame.map(QAbstractVideoBuffer::ReadOnly))
        {
            // 转换为QImage
            QImage image = frameToImage(cloneFrame);
            if (!image.isNull())
            {
                emit frameAvailable(image);
            }
            cloneFrame.unmap();
        }
        return true;
    }
    return false;
}

QImage VideoSurface::frameToImage(const QVideoFrame &frame) const
{
    if (!frame.isValid())
        return QImage();

    if (frame.handleType() == QAbstractVideoBuffer::NoHandle)
    {
        QImage::Format imageFormat = QImage::Format_Invalid;

        switch (frame.pixelFormat())
        {
        case QVideoFrame::Format_RGB32:
            imageFormat = QImage::Format_RGB32;
            break;
        case QVideoFrame::Format_ARGB32:
            imageFormat = QImage::Format_ARGB32;
            break;
        case QVideoFrame::Format_ARGB32_Premultiplied:
            imageFormat = QImage::Format_ARGB32_Premultiplied;
            break;
        case QVideoFrame::Format_RGB24:
            imageFormat = QImage::Format_RGB888;
            break;
        case QVideoFrame::Format_RGB565:
            imageFormat = QImage::Format_RGB16;
            break;
        default:
            return QImage();
        }

        if (imageFormat != QImage::Format_Invalid)
        {
            QImage image(frame.bits(),
                         frame.width(),
                         frame.height(),
                         frame.bytesPerLine(),
                         imageFormat);
            return image.copy(); // 返回副本，因为原始帧可能被回收
        }
    }

    return QImage();
}