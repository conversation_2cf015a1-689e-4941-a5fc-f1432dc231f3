# [root]

# logfile = logs/httpd.log
# loglevel = [VERBOSE,DEBUG,INFO,WARN,ERROR,FATAL,SILENT]
loglevel = INFO
log_remain_days = 3
log_filesize = 64M

# worker_processes = auto # auto = ncpu
worker_processes = 4

# http server
port = 8080
#base_url = /v1/api
document_root = html
home_page = index.html
#error_page = error.html
index_of = /downloads/

# SSL/TLS
ssl = off
ssl_certificate = cert/server.crt
ssl_privatekey = cert/server.key
ssl_ca_certificate = cert/cacert.pem
