#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

// 认证设置宏定义
#define DEFAULT_USERNAME "root"
#define DEFAULT_PASSWORD "LzXa#2023*"
#define DEFAULT_STREAM_PATH "stream=0"

#include <QObject>
#include <QSettings>
#include <QString>
#include <QSqlDatabase>
#include <QList>
#include "camerascanner.h"

class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager &instance();

    // 获取配置对象
    QSettings *getSettings() const;

    // 扫描设置
    QString getIpPrefix() const;
    void setIpPrefix(const QString &prefix);

    int getStartIp() const;
    void setStartIp(int ip);

    int getEndIp() const;
    void setEndIp(int ip);

    // 认证设置
    QString getUsername() const;
    void setUsername(const QString &username);

    QString getPassword() const;
    void setPassword(const QString &password);

    QString getStreamPath() const;
    void setStreamPath(const QString &path);

    // 超时设置
    int getRtspTimeout() const;
    void setRtspTimeout(int timeout);

    bool getUseHttpDetection() const;
    void setUseHttpDetection(bool use);

    int getHttpTimeout() const;
    void setHttpTimeout(int timeout);

    // 录制设置
    int getMaxRecordingDuration() const;
    void setMaxRecordingDuration(int seconds);

    // 自动显示设置
    bool getAutoDisplay() const;
    void setAutoDisplay(bool autoDisplay);

    // 默认分屏设置
    int getDefaultDisplayMode() const;
    void setDefaultDisplayMode(int mode);

    // 应用程序信息
    QString getAppName() const;
    void setAppName(const QString &appName);

    QString getAppVersion() const;
    void setAppVersion(const QString &appVersion);

    // 日志设置
    int getLogLevel() const;
    void setLogLevel(int level);

    // 日志文件名设置
    QString getLogFileName() const;
    void setLogFileName(const QString &fileName);

    // 保存所有设置到文件
    void saveSettings();

    // 数据库相关方法
    bool isFirstScan() const;
    void setFirstScanCompleted();
    bool initDatabase();
    bool saveCameraToDatabase(const CameraInfo &camera);
    QList<CameraInfo> loadCamerasFromDatabase();
    bool updateCameraName(const QString &rtspUrl, const QString &newName);

    // 根据IP地址查找摄像头信息
    CameraInfo findCameraByIp(const QString &ipAddress);

    // 删除摄像头
    bool deleteCameraByIp(const QString &ipAddress);

    // 删除不在列表中的所有摄像头
    bool deleteInactiveCameras(const QList<QString> &activeIpAddresses);

    // 删除指定IP段内不在活跃列表中的摄像头
    bool deleteInactiveCameras(const QList<QString> &activeIpAddresses, const QList<QString> &scanRangeIps);

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 禁止复制
    ConfigManager(const ConfigManager &) = delete;
    ConfigManager &operator=(const ConfigManager &) = delete;

    // 加载设置
    void loadSettings();

    QSettings *m_settings;

    // 扫描设置
    QString m_ipPrefix;
    int m_startIp;
    int m_endIp;

    // 认证设置
    QString m_username;
    QString m_password;
    QString m_streamPath;

    // 超时设置
    int m_rtspTimeout;
    bool m_useHttpDetection;
    int m_httpTimeout;

    // 录制设置
    int m_maxRecordingDuration;

    // 自动显示设置
    bool m_autoDisplay;

    // 默认分屏设置
    int m_defaultDisplayMode;

    // 应用程序信息
    QString m_appName;
    QString m_appVersion;

    // 日志设置
    int m_logLevel;

    // 数据库相关
    QSqlDatabase m_database;
    bool m_firstScan;
};

#endif // CONFIGMANAGER_H