#!/bin/bash

confile=hconfig.h
# start confile
cat << END > $confile
#ifndef HV_CONFIG_H_
#define HV_CONFIG_H_

END

# Checks for compiler
echo "checking for compiler..."
if [ $CROSS_COMPILE ]; then
    CC=${CROSS_COMPILE}gcc
    CXX=${CROSS_COMPILE}g++
fi

if [ ! $CC ]; then
    CC=gcc
    CXX=g++
fi
CC_VERSION=`$CC --version 2>&1 | head -n 1`

echo "CC  = $CC"
echo "CXX = $CXX"
echo "$CC_VERSION"

# shell functions

write_define() {
cat << END >> hconfig.h
#ifndef $macro
#define $macro $value
#endif

END
}

mkdir tmp 2>/dev/null
check_header() {
echo -n "checking for $header... "
rm tmp/check 2>/dev/null
cat << END > tmp/check.c
#include <$header>

int main() {
    return 0;
}

END
$CC -o tmp/check tmp/check.c 2>/dev/null
if [ -x tmp/check ]; then
    value=1
    echo "yes"
else
    value=0
    echo "no"
fi
macro=HAVE_$(echo $header | tr a-z./ A-Z__)
write_define
}

check_function() {
echo -n "checking for $function... "
rm tmp/check 2>/dev/null
cat << END > tmp/check.c
#include <$header>

int $function(void** pp) {return 0;}
int main() {
    void* p;
    return $function(&p);
}

END
$CC -o tmp/check tmp/check.c 2>/dev/null
if [ -x tmp/check ]; then
    value=0
    echo "no"
else
    value=1
    echo "yes"
fi
macro=HAVE_$(echo $function | tr a-z A-Z)
write_define
}

check_option() {
value=$(eval echo \$$option)
echo "checking for $option=$value"
if [ "$value" == "yes" ]; then
cat << END >> $confile
#define $option 1
END
else
cat << END >> $confile
/* #undef $option */
END
fi
}

# Checks for os
echo "checking for os..."
HOST_OS=`uname -s`
HOST_ARCH=`uname -m`
TARGET_PLATFORM=`$CC -v 2>&1 | grep Target | sed 's/Target: //'`
TARGET_ARCH=`echo $TARGET_PLATFORM | awk -F'-' '{print $1}'`

case $TARGET_PLATFORM in
    *mingw*) TARGET_OS=Windows ;;
    *android*) TARGET_OS=Android ;;
    *darwin*) TARGET_OS=Darwin ;;
    *) TARGET_OS=Linux ;;
esac

echo "HOST_OS         = $HOST_OS"
echo "HOST_ARCH       = $HOST_ARCH"
echo "TARGET_PLATFORM = $TARGET_PLATFORM"
echo "TARGET_OS       = $TARGET_OS"
echo "TARGET_ARCH     = $TARGET_ARCH"

# Checks for programs

# Checks for libraries

# Checks for header files
header=stdbool.h && check_header
header=stdint.h && check_header
header=stdatomic.h && check_header
header=sys/types.h && check_header
header=sys/stat.h && check_header
header=sys/time.h && check_header
header=fcntl.h && check_header
header=pthread.h && check_header

# Checks for functions
function=gettid && header=unistd.h && check_function
function=strlcpy && header=string.h && check_function
function=strlcat && header=string.h && check_function
function=clock_gettime && header=time.h && check_function
function=gettimeofday && header=sys/time.h && check_function
function=pthread_spin_lock && header=pthread.h && check_function
function=pthread_mutex_timedlock && header=pthread.h && check_function
function=sem_timedwait && header=semaphore.h && check_function

# Checks for options
source config.mk
option=ENABLE_UDS && check_option
option=USE_MULTIMAP && check_option

# end confile
cat << END >> $confile

#endif // HV_CONFIG_H_
END

echo "configure done."
