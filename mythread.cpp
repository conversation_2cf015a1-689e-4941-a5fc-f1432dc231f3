﻿#include "mythread.h"
#include "videosurface.h"
#include <QTcpSocket>
#include <QByteArray>
#include <QImage>
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QEventLoop>
#include <QTimer>
#include <QUrl>
#include <QDebug>
#include <QRegExp>
#include <QProcess>
#include <QTemporaryFile>
#include <QDir>
#include <QCoreApplication>
#include <QTime>
#include <QMediaPlayer>
#include <QAbstractVideoSurface>
#include <QVideoSurfaceFormat>
#include <QVideoFrame>
#include <QElapsedTimer>
#include <QMutexLocker>
#include "logmanager.h"
// FFmpeg头文件 - 条件编译
#ifdef USE_FFMPEG
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/opt.h> // 添加这个头文件，提供av_opt_set_double函数
#endif

// 定义LOG_DEBUG_ONCE宏，只打印一次日志
#define LOG_DEBUG_ONCE(msg)         \
	do                              \
	{                               \
		static bool logged = false; \
		if (!logged)                \
		{                           \
			LOG_DEBUG(msg);         \
			logged = true;          \
		}                           \
	} while (0)

MyThread::MyThread(QObject *parent) : QThread(parent), mediaPlayer(nullptr), videoSurface(nullptr)
#ifdef USE_FFMPEG
									  ,
									  formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
									  audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
#endif
{
	isRtspMode = false;
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
	hasValidLastFrame = false; // 初始化最后一帧标志
	consecutiveGrayFrames = 0; // 初始化连续灰白帧计数
	backupFrameIndex = 0;	   // 初始化备用帧索引
}

MyThread::MyThread(QString ip, int port, QObject *parent) : QThread(parent), mediaPlayer(nullptr), videoSurface(nullptr)
#ifdef USE_FFMPEG
															,
															formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
															audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
#endif
{
	this->ip = ip;
	this->port = port;
	isRtspMode = false;
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
	hasValidLastFrame = false; // 初始化最后一帧标志
	consecutiveGrayFrames = 0; // 初始化连续灰白帧计数
	backupFrameIndex = 0;	   // 初始化备用帧索引
}

MyThread::MyThread(QString rtspUrl, QObject *parent) : QThread(parent), mediaPlayer(nullptr), videoSurface(nullptr)
#ifdef USE_FFMPEG
													   ,
													   formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
													   audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
#endif
{
	this->rtspUrl = rtspUrl;
	isRtspMode = true;
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
	hasValidLastFrame = false; // 初始化最后一帧标志
	consecutiveGrayFrames = 0; // 初始化连续灰白帧计数
	backupFrameIndex = 0;	   // 初始化备用帧索引
}

MyThread::~MyThread()
{
	// 停止所有活动
	isRunning = false;
	m_threadStopped = false;

	// 尝试正常停止线程
	if (QThread::isRunning())
	{
		// 给线程100毫秒的时间正常结束
		if (!wait(100))
		{
			// 如果100毫秒后线程还没结束，强制终止
			LOG_WARNING("Force terminating thread in destructor");
			terminate();
			wait(500); // 等待线程真正结束
		}
	}

	// 清理资源
	mutex.lock();
	cleanupMediaResources();
#ifdef USE_FFMPEG
	cleanupFFmpeg();
#endif
	mutex.unlock();

	LOG_DEBUG("MyThread destructor completed");
}

// 新增: 安全停止线程方法
bool MyThread::safeStop(int timeoutMs)
{
	if (!isRunning)
	{
		return true; // 线程已经停止
	}

	// 设置停止标志
	isRunning = false;
	m_threadStopped = false;

	// 等待线程完成
	if (QThread::isRunning())
	{
		// 等待指定时间
		return wait(timeoutMs);
	}

	return true;
}

void MyThread::setRunning(bool running)
{
	if (!running && isRunning)
	{
		// 如果要停止线程，使用安全停止方法
		safeStop();
	}
	else
	{
		isRunning = running;
	}
}

void MyThread::run()
{
	LOG_DEBUG("MyThread start running in thread:" << QThread::currentThread());

	// 在线程开始时设置运行标志
	isRunning = true;
	m_threadStopped = false;

	if (isRtspMode)
	{
		LOG_DEBUG("isRtspMode");

#ifdef USE_FFMPEG
		// 使用FFmpeg处理RTSP流
		handleRtspWithFFmpeg();

		// 如果上面的方法失败，可以尝试传统RTSP连接作为备选方案
		// if (!isRunning)
		// {
		// 	LOG_DEBUG("FFmpeg处理RTSP失败，尝试传统RTSP连接");
		// 	isRunning = true;
		// 	handleRtspConnection();
		// }
#else
		// 直接使用传统RTSP连接方式
		LOG_DEBUG("未启用FFmpeg支持，使用传统RTSP连接");
		handleRtspConnection();
#endif
	}
	else
	{
		LOG_DEBUG("is not RTSP mode");
		// 使用TCP Socket连接IP和端口
		handleTcpConnection();
	}

	// 线程即将结束，标记线程已停止
	m_threadStopped = true;

	LOG_DEBUG("MyThread end running in thread:" << QThread::currentThread());
}

void MyThread::handleMediaPlayerError(QMediaPlayer::Error error)
{
	// 记录错误信息
	QString errorString = mediaPlayer ? mediaPlayer->errorString() : "Unknown error";
	LOG_ERROR("MediaPlayer error: " << error << errorString);

	// 停止播放并清理资源
	mutex.lock();

	// 只在mediaPlayer存在时停止它
	if (mediaPlayer)
	{
		try
		{
			mediaPlayer->stop();
		}
		catch (...)
		{
			LOG_ERROR("Exception when stopping mediaPlayer in error handler");
		}
	}

	// 设置停止标志并发送断开信号
	isRunning = false;

	// 解锁互斥锁，以便断开连接处理可以在不死锁的情况下获取资源
	mutex.unlock();

	// 发送断开连接信号
	emit disconnectSlot();
}

void MyThread::handleMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
	LOG_DEBUG("MediaPlayer status changed: " << status);

	// 如果媒体结束或者无效，发出断开信号
	if (status == QMediaPlayer::EndOfMedia ||
		status == QMediaPlayer::InvalidMedia ||
		status == QMediaPlayer::UnknownMediaStatus)
	{
		// 加锁保护共享资源访问
		mutex.lock();

		// 设置停止标志
		isRunning = false;

		// 解锁互斥锁，以便断开连接处理可以在不死锁的情况下获取资源
		mutex.unlock();

		// 发送断开连接信号
		emit disconnectSlot();
	}
}

// 获取最后一帧图像
QImage MyThread::getLastFrame() const
{
	// 使用互斥锁保护lastFrame的访问
	QMutexLocker locker(&mutex);
	return lastFrame;
}

// 实现设置静音状态的方法
void MyThread::setMuted(bool muted)
{
	isMuted = muted;

#ifdef USE_FFMPEG
	// 处理FFmpeg模式下的静音
	if (!muted)
	{
		LOG_DEBUG("FFmpeg mode: MediaPlayer unmuted, set volume to 100");

		// 在音频处理循环中使用isMuted标志
		LOG_DEBUG("FFmpeg mode: Audio unmuted flag set, isMuted = " << isMuted);

		// 检查音频流是否存在
		bool hasAudioStream = false;
		int audioStreamIndex = -1;

		// 检查解码器和音频流
		if (codecContext)
		{
			LOG_DEBUG("FFmpeg mode: codecContext exists, codec_type = " << codecContext->codec_type);
		}
		else
		{
			LOG_DEBUG("FFmpeg mode: codecContext is NULL");
		}

		if (formatContext)
		{
			LOG_DEBUG("FFmpeg mode: formatContext exists, nb_streams = " << formatContext->nb_streams);

			// 查找并记录所有流的类型
			for (unsigned int i = 0; i < formatContext->nb_streams; i++)
			{
				AVMediaType type = formatContext->streams[i]->codecpar->codec_type;
				LOG_DEBUG("FFmpeg mode: Stream " << i << " type = " << type);

				if (type == AVMEDIA_TYPE_AUDIO)
				{
					hasAudioStream = true;
					audioStreamIndex = i;
					LOG_DEBUG("FFmpeg mode: Found audio stream at index " << i);

					// 尝试设置音量元数据
					av_dict_set(&formatContext->streams[i]->metadata, "volume", "1.0", 0);

					// 在新版FFmpeg中，不再使用AVStream->codec
					// 我们已经创建了codecContext，可以直接使用
					LOG_DEBUG("FFmpeg mode: Setting volume through stream metadata");

					break; // 只处理第一个音频流
				}
			}

			if (!hasAudioStream)
			{
				LOG_DEBUG("FFmpeg mode: No audio stream found in this media!");
			}
		}
		else
		{
			LOG_DEBUG("FFmpeg mode: formatContext is NULL");
		}

		// 检查音频处理函数
		LOG_DEBUG("FFmpeg mode: Checking if audio processing function is properly hooked");
		// 这里可以添加更多日志来检查音频处理路径
	}
	else
	{
		LOG_DEBUG("FFmpeg mode: MediaPlayer muted");

		// 在音频处理循环中使用isMuted标志
		LOG_DEBUG("FFmpeg mode: Audio muted flag set, isMuted = " << isMuted);

		// 检查音频流是否存在
		bool hasAudioStream = false;
		int audioStreamIndex = -1;

		if (formatContext)
		{
			// 查找音频流
			for (unsigned int i = 0; i < formatContext->nb_streams; i++)
			{
				if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO)
				{
					hasAudioStream = true;
					audioStreamIndex = i;
					LOG_DEBUG("FFmpeg mode: Found audio stream at index " << i << " (muting)");

					// 尝试设置音量元数据
					av_dict_set(&formatContext->streams[i]->metadata, "volume", "0.0", 0);

					// 在新版FFmpeg中，不再使用AVStream->codec
					// 我们已经创建了codecContext，可以直接使用
					LOG_DEBUG("FFmpeg mode: Setting volume to 0 through stream metadata");

					break; // 只需处理第一个音频流
				}
			}

			if (!hasAudioStream)
			{
				LOG_DEBUG("FFmpeg mode: No audio stream found to mute!");
			}
		}
	}
#else
	if (mediaPlayer)
	{
		mediaPlayer->setMuted(muted);
		// 如果取消静音，确保音量足够大
		if (!muted)
		{
			LOG_DEBUG("MediaPlayer unmuted, set volume to 100");
			mediaPlayer->setVolume(100);
		}
		LOG_DEBUG("MediaPlayer muted: " << muted << ", volume: " << mediaPlayer->volume());
	}

#endif
}

void MyThread::handleRtspWithMediaPlayer()
{
	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - use MediaPlayer to open RTSP stream: " << rtspUrl);

	// 实例互斥锁保护本实例的资源
	mutex.lock();

	try
	{
		// 清理之前的资源
		cleanupMediaResources();

		// 创建视频表面 - 这一步在锁外进行
		mutex.unlock();
		VideoSurface *newVideoSurface = new VideoSurface();
		mutex.lock();

		if (!newVideoSurface)
		{
			LOG_ERROR("Failed to create VideoSurface");
			isRunning = false;
			mutex.unlock();
			emit disconnectSlot();
			return;
		}

		// 创建媒体播放器
		QMediaPlayer *newMediaPlayer = new QMediaPlayer(nullptr, QMediaPlayer::VideoSurface);

		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 释放全局多媒体资源锁");

		if (!newMediaPlayer)
		{
			LOG_ERROR("Failed to create QMediaPlayer");
			delete newVideoSurface;
			isRunning = false;
			mutex.unlock();
			emit disconnectSlot();
			return;
		}

		// 连接信号槽 - 在实例锁保护下进行
		connect(newVideoSurface, &VideoSurface::frameAvailable, this, [this](const QImage &image)
				{
			if (isRunning)
			{
				mutex.lock();
				lastFrame = image; // 保存最后一帧
				mutex.unlock();
				emit transmitData(image);
			} }, Qt::QueuedConnection); // 使用队列连接确保线程安全

		// 连接其他信号槽
		connect(newMediaPlayer, static_cast<void (QMediaPlayer::*)(QMediaPlayer::Error)>(&QMediaPlayer::error),
				this, &MyThread::handleMediaPlayerError, Qt::QueuedConnection);

		connect(newMediaPlayer, &QMediaPlayer::mediaStatusChanged,
				this, &MyThread::handleMediaStatusChanged, Qt::QueuedConnection);

		connect(newMediaPlayer, &QMediaPlayer::audioAvailableChanged, this, [this](bool available)
				{ LOG_DEBUG("MediaPlayer audio available changed: " << available); }, Qt::QueuedConnection);

		// 设置静音状态
		newMediaPlayer->setMuted(isMuted);
		LOG_DEBUG("MediaPlayer default muted: " << isMuted);
		LOG_DEBUG("MediaPlayer volume: " << newMediaPlayer->volume());

		// 设置视频输出 - 这一步需要静态互斥锁保护
		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 准备设置视频输出，这是一个可能崩溃的操作点");

		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 再次获取全局锁用于setVideoOutput");

		bool setOutputSuccess = false;

		try
		{
			// 执行关键操作
			newMediaPlayer->setVideoOutput(newVideoSurface);
			setOutputSuccess = true;
			LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 设置视频输出成功");
		}
		catch (const std::exception &e)
		{
			LOG_ERROR("Exception setting video output: " << e.what());
		}
		catch (...)
		{
			LOG_ERROR("Unknown exception setting video output");
		}

		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 释放全局锁");

		if (!setOutputSuccess)
		{
			// 清理刚创建的资源
			delete newMediaPlayer;
			delete newVideoSurface;
			isRunning = false;
			mutex.unlock();
			emit disconnectSlot();
			return;
		}

		// 设置成员变量
		mediaPlayer = newMediaPlayer;
		videoSurface = newVideoSurface;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("Exception in handleRtspWithMediaPlayer: " << e.what());
		isRunning = false;
		mutex.unlock();
		emit disconnectSlot();
		return;
	}
	catch (...)
	{
		LOG_ERROR("Unknown exception in handleRtspWithMediaPlayer");
		isRunning = false;
		mutex.unlock();
		emit disconnectSlot();
		return;
	}

	// 解锁互斥锁
	mutex.unlock();
	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 准备设置媒体源");

	// 设置媒体源
	try
	{
		mediaPlayer->setMedia(QUrl(rtspUrl));
		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 媒体源设置成功");
	}
	catch (...)
	{
		LOG_ERROR("Thread: " << QThread::currentThreadId() << " - 设置媒体源时发生异常");
		mutex.lock();
		cleanupMediaResources();
		isRunning = false;
		mutex.unlock();
		emit disconnectSlot();
		return;
	}

	// 开始播放
	try
	{
		mediaPlayer->play();
		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 开始播放");
	}
	catch (...)
	{
		LOG_ERROR("Thread: " << QThread::currentThreadId() << " - 开始播放时发生异常");
		mutex.lock();
		cleanupMediaResources();
		isRunning = false;
		mutex.unlock();
		emit disconnectSlot();
		return;
	}

	// 等待一段时间，看是否成功获取视频帧
	QTime timeout;
	timeout.start();
	bool receivedFrame = false;

	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 等待获取视频帧");
	while (isRunning && timeout.elapsed() < 5000)
	{ // 5秒超时
		QCoreApplication::processEvents();
		QThread::msleep(100);

		QMediaPlayer::MediaStatus status = QMediaPlayer::UnknownMediaStatus;

		mutex.lock();
		if (mediaPlayer)
		{
			status = mediaPlayer->mediaStatus();
		}
		mutex.unlock();

		if (status == QMediaPlayer::BufferedMedia || status == QMediaPlayer::LoadedMedia)
		{
			receivedFrame = true;
			LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 成功接收到视频帧");
			break;
		}

		if (status == QMediaPlayer::InvalidMedia)
		{
			LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 媒体无效");
			break;
		}
	}

	if (!receivedFrame)
	{
		LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - MediaPlayer方法失败");

		// 使用锁保护资源清理
		mutex.lock();
		cleanupMediaResources();
		mutex.unlock();

		isRunning = false;
		return;
	}

	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 使用MediaPlayer播放RTSP流成功");

	// 持续处理事件，直到线程停止
	while (isRunning)
	{
		QCoreApplication::processEvents();
		QThread::msleep(50);
	}

	// 线程结束，清理资源
	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 线程停止，清理资源");
	mutex.lock();
	cleanupMediaResources();
	mutex.unlock();
}

// 添加辅助方法用于清理媒体资源
void MyThread::cleanupMediaResources()
{
	try
	{
		// 必须在获取mutex锁之后调用
		LOG_DEBUG("开始清理媒体资源");

		// 对于删除mediaPlayer和videoSurface，我们也需要获取静态锁
		// 但不能在已经持有实例锁的情况下直接获取，避免死锁
		// 这里假设调用者已经获取了实例锁

		if (mediaPlayer)
		{
			try
			{
				mediaPlayer->disconnect(); // 先断开所有连接
				mediaPlayer->stop();

				// 获取静态锁以安全删除媒体资源
				delete mediaPlayer;

				mediaPlayer = nullptr;
				LOG_DEBUG("清理mediaPlayer成功");
			}
			catch (...)
			{
				LOG_ERROR("清理mediaPlayer时发生异常");
			}
		}

		if (videoSurface)
		{
			try
			{
				videoSurface->disconnect(); // 断开所有连接

				delete videoSurface;

				videoSurface = nullptr;
				LOG_DEBUG("清理videoSurface成功");
			}
			catch (...)
			{
				LOG_ERROR("清理videoSurface时发生异常");
			}
		}

		LOG_DEBUG("媒体资源清理完成");
	}
	catch (...)
	{
		LOG_ERROR("清理媒体资源时发生未知异常");
	}
}

void MyThread::handleTcpConnection()
{
	QTcpSocket socket;
	connect(&socket, SIGNAL(disconnected()), this, SIGNAL(disconnectSlot()));
	socket.connectToHost(ip, port);
	//	socket.connectToHost("**************", 10086);
	if (!socket.waitForConnected(3000))
	{
		LOG_ERROR("connect failed:" << socket.errorString());
		emit disconnectSlot();
		return;
	}
	else
	{
		LOG_DEBUG("connect success!");
	}

	// 发送HTTP请求
	socket.write("\r\n\r\n");
	if (socket.waitForBytesWritten(3000))
	{
		LOG_DEBUG("send Http Request success!");
	}
	else
	{
		LOG_DEBUG("send Http Request failed!");
		return;
	}

	// 读取响应
	QByteArray dataStream;
	QRegExp rx("\\d+");
	while (isRunning)
	{
		if (socket.waitForReadyRead())
		{
			dataStream = socket.readLine();
			if (dataStream.contains("Content-Length"))
			{
				int pos = rx.indexIn(dataStream);
				if (pos > -1)
				{
					pos = rx.cap(0).toInt();
					dataStream.resize(pos);
					socket.readLine();
					socket.read(dataStream.data(), pos);
					QImage image = QImage::fromData(dataStream, "JPEG");

					// 使用互斥锁保护lastFrame的访问
					mutex.lock();
					lastFrame = image; // 保存最后一帧
					mutex.unlock();

					emit transmitData(image);
				}
			}
		}
	}
}

void MyThread::handleRtspConnection()
{
	LOG_DEBUG("Thread: " << QThread::currentThreadId() << " - 连接RTSP流: " << rtspUrl);

	// 设置连接超时
	int connectionTimeout = 5000; // 增加超时时间以提高稳定性
	int readTimeout = 3000;
	int writeTimeout = 3000;

	// 解析RTSP URL
	QUrl url(rtspUrl);
	QString host = url.host();
	int port = url.port(554); // RTSP默认端口是554
	QString path = url.path();
	QString query = url.query();

	if (!query.isEmpty())
	{
		path += "?" + query;
	}

	// 手动从RTSP URL中提取认证信息，避免QUrl处理特殊字符时的问题
	QString credentials;
	if (rtspUrl.contains("@"))
	{
		int protocolEnd = rtspUrl.indexOf("://") + 3;
		int authEnd = rtspUrl.indexOf("@", protocolEnd);
		if (authEnd > protocolEnd)
		{
			credentials = rtspUrl.mid(protocolEnd, authEnd - protocolEnd);
			LOG_DEBUG("提取的认证信息: " << credentials.left(credentials.indexOf(':')) << ":******");
		}
	}

	LOG_DEBUG("连接到RTSP服务器: " << host << " 端口: " << port << " 路径: " << path);

	// 创建RTSP连接套接字
	QTcpSocket socket;

	// 设置连接超时处理
	socket.connectToHost(host, port);

	if (!socket.waitForConnected(connectionTimeout))
	{
		LOG_ERROR("RTSP连接失败: " << socket.errorString());
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	// 添加错误处理
	connect(&socket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(handleSocketError()));
	connect(&socket, SIGNAL(disconnected()), this, SLOT(handleSocketError()));

	LOG_DEBUG("RTSP连接成功");

	// 准备认证头
	QString authHeader = "";
	if (!credentials.isEmpty())
	{
		// 使用Basic认证
		QByteArray auth = credentials.toUtf8().toBase64();
		authHeader = QString("Authorization: Basic %1\r\n").arg(QString(auth));
	}

	// RTSP通信 - 步骤1: OPTIONS
	QString optionsRequest = QString(
								 "OPTIONS %1 RTSP/1.0\r\n"
								 "CSeq: 1\r\n"
								 "%2"
								 "User-Agent: LiveCamera RTSP Client\r\n"
								 "\r\n")
								 .arg(path)
								 .arg(authHeader);

	LOG_DEBUG("发送OPTIONS请求");
	socket.write(optionsRequest.toUtf8());

	if (!socket.waitForBytesWritten(writeTimeout))
	{
		LOG_ERROR("发送OPTIONS请求失败");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	if (!socket.waitForReadyRead(readTimeout))
	{
		LOG_ERROR("OPTIONS请求无响应");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	QByteArray response = socket.readAll();
	if (!response.contains("200 OK"))
	{
		LOG_ERROR("OPTIONS请求返回错误: " << response);
		isRunning = false;
		emit disconnectSlot();
		return;
	}
	LOG_DEBUG("OPTIONS请求成功");

	// RTSP通信 - 步骤2: DESCRIBE
	QString describeRequest = QString(
								  "DESCRIBE %1 RTSP/1.0\r\n"
								  "CSeq: 2\r\n"
								  "%2"
								  "Accept: application/sdp\r\n"
								  "User-Agent: LiveCamera RTSP Client\r\n"
								  "\r\n")
								  .arg(path)
								  .arg(authHeader);

	LOG_DEBUG("发送DESCRIBE请求");
	socket.write(describeRequest.toUtf8());

	if (!socket.waitForBytesWritten(writeTimeout))
	{
		LOG_ERROR("发送DESCRIBE请求失败");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	if (!socket.waitForReadyRead(readTimeout))
	{
		LOG_ERROR("DESCRIBE请求无响应");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	response = socket.readAll();
	if (!response.contains("200 OK"))
	{
		LOG_ERROR("DESCRIBE请求返回错误: " << response);
		isRunning = false;
		emit disconnectSlot();
		return;
	}
	LOG_DEBUG("DESCRIBE请求成功");

	// RTSP通信 - 步骤3: SETUP
	// 使用可用的客户端端口
	QString setupRequest = QString(
							   "SETUP %1 RTSP/1.0\r\n"
							   "CSeq: 3\r\n"
							   "%2"
							   "Transport: RTP/AVP;unicast;client_port=16000-16001\r\n"
							   "User-Agent: LiveCamera RTSP Client\r\n"
							   "\r\n")
							   .arg(path)
							   .arg(authHeader);

	LOG_DEBUG("发送SETUP请求");
	socket.write(setupRequest.toUtf8());

	if (!socket.waitForBytesWritten(writeTimeout))
	{
		LOG_ERROR("发送SETUP请求失败");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	if (!socket.waitForReadyRead(readTimeout))
	{
		LOG_ERROR("SETUP请求无响应");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	response = socket.readAll();
	if (!response.contains("200 OK"))
	{
		LOG_ERROR("SETUP请求返回错误: " << response);
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	// 解析会话ID
	QRegExp sessionRx("Session: ([^;\\r\\n]+)");
	QString sessionId;
	if (sessionRx.indexIn(QString::fromUtf8(response)) != -1)
	{
		sessionId = sessionRx.cap(1);
		LOG_DEBUG("获取会话ID: " << sessionId);
	}
	else
	{
		LOG_ERROR("未找到会话ID");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	// RTSP通信 - 步骤4: PLAY
	QString playRequest = QString(
							  "PLAY %1 RTSP/1.0\r\n"
							  "CSeq: 4\r\n"
							  "%2"
							  "Session: %3\r\n"
							  "Range: npt=0.000-\r\n"
							  "User-Agent: LiveCamera RTSP Client\r\n"
							  "\r\n")
							  .arg(path)
							  .arg(authHeader)
							  .arg(sessionId);

	LOG_DEBUG("发送PLAY请求");
	socket.write(playRequest.toUtf8());

	if (!socket.waitForBytesWritten(writeTimeout))
	{
		LOG_ERROR("发送PLAY请求失败");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	if (!socket.waitForReadyRead(readTimeout))
	{
		LOG_ERROR("PLAY请求无响应");
		isRunning = false;
		emit disconnectSlot();
		return;
	}

	response = socket.readAll();
	if (!response.contains("200 OK"))
	{
		LOG_ERROR("PLAY请求返回错误: " << response);
		isRunning = false;
		emit disconnectSlot();
		return;
	}
	LOG_DEBUG("PLAY请求成功，开始接收视频流");

	// 处理数据流
	QByteArray dataBuffer;
	QRegExp frameRx("\\xFF\\xD8[\\s\\S]*\\xFF\\xD9"); // JPEG帧标记

	QTime dataTimeout;
	dataTimeout.start();
	bool receivedFrame = false;

	// 尝试通过RTSP接收数据，最多尝试10秒
	LOG_DEBUG("尝试接收视频数据...");
	while (isRunning && dataTimeout.elapsed() < 10000 && !receivedFrame)
	{
		// 检查是否有数据可读
		if (socket.waitForReadyRead(100))
		{
			dataBuffer.append(socket.readAll());

			// 查找JPEG帧
			int pos = frameRx.indexIn(QString::fromLatin1(dataBuffer));
			if (pos != -1)
			{
				QByteArray frameData = dataBuffer.mid(pos, frameRx.matchedLength());
				dataBuffer.remove(0, pos + frameRx.matchedLength());

				QImage image = QImage::fromData(frameData, "JPEG");
				if (!image.isNull())
				{
					mutex.lock();
					lastFrame = image; // 保存最后一帧
					mutex.unlock();

					emit transmitData(image);
					receivedFrame = true;
					LOG_DEBUG("成功接收第一帧视频数据");
				}
			}
		}
	}

	if (!receivedFrame)
	{
		LOG_ERROR("未能接收到有效的视频帧，切换到备选方法");

		// 尝试使用HTTP方式作为备选方案
		// 关闭当前连接
		socket.disconnectFromHost();
		if (socket.state() != QAbstractSocket::UnconnectedState)
			socket.waitForDisconnected(1000);

		isRunning = false;
		return;
	}

	// 持续接收和处理视频流
	LOG_DEBUG("开始持续接收视频流");

	while (isRunning)
	{
		// 定期发送保活请求以维持会话
		static QTime keepAliveTimer;
		if (keepAliveTimer.elapsed() > 30000 || !keepAliveTimer.isValid()) // 每30秒发送一次保活请求
		{
			QString getParamRequest = QString(
										  "GET_PARAMETER %1 RTSP/1.0\r\n"
										  "CSeq: 5\r\n"
										  "%2"
										  "Session: %3\r\n"
										  "User-Agent: LiveCamera RTSP Client\r\n"
										  "\r\n")
										  .arg(path)
										  .arg(authHeader)
										  .arg(sessionId);

			socket.write(getParamRequest.toUtf8());
			socket.waitForBytesWritten(1000);
			keepAliveTimer.start();
		}

		// 等待新数据到达
		if (socket.waitForReadyRead(100) || socket.bytesAvailable() > 0)
		{
			dataBuffer.append(socket.readAll());

			// 查找并处理所有的JPEG帧
			int pos = 0;
			while ((pos = frameRx.indexIn(QString::fromLatin1(dataBuffer), pos)) != -1)
			{
				QByteArray frameData = dataBuffer.mid(pos, frameRx.matchedLength());
				QImage image = QImage::fromData(frameData, "JPEG");
				if (!image.isNull())
				{
					mutex.lock();
					lastFrame = image; // 保存最后一帧
					mutex.unlock();

					emit transmitData(image);
				}
				pos += frameRx.matchedLength();
			}

			// 删除已处理的数据
			if (pos > 0)
			{
				dataBuffer.remove(0, pos);
			}

			// 限制缓冲区大小
			if (dataBuffer.size() > 2000000) // 2MB
			{
				dataBuffer.remove(0, dataBuffer.size() - 200000); // 保留最后200KB
			}
		}
		else
		{
			// 处理无数据的情况
			QCoreApplication::processEvents();
			QThread::msleep(10);
		}
	}

	// 线程结束，发送TEARDOWN请求
	if (socket.state() == QAbstractSocket::ConnectedState)
	{
		QString teardownRequest = QString(
									  "TEARDOWN %1 RTSP/1.0\r\n"
									  "CSeq: 6\r\n"
									  "%2"
									  "Session: %3\r\n"
									  "User-Agent: LiveCamera RTSP Client\r\n"
									  "\r\n")
									  .arg(path)
									  .arg(authHeader)
									  .arg(sessionId);

		LOG_DEBUG("发送TEARDOWN请求");
		socket.write(teardownRequest.toUtf8());
		socket.waitForBytesWritten(1000);
		socket.waitForDisconnected(1000);
	}

	LOG_DEBUG("RTSP连接已关闭");
}

// 测试RTSP连接是否有效（使用Socket方式）
bool MyThread::testRtspConnection(const QString &rtspUrl, int timeoutMs)
{
	LOG_DEBUG("Testing RTSP connection: " << rtspUrl);

	// 使用严格超时控制
	QElapsedTimer timeoutTimer;
	timeoutTimer.start();

	// 检查URL是否有效
	QUrl url(rtspUrl);
	if (!url.isValid())
	{
		LOG_DEBUG("RTSP connection error: Invalid URL format");
		return false;
	}

	// 从URL解析主机名和端口
	QString host = url.host();
	int port = url.port(554); // 默认RTSP端口为554

	LOG_DEBUG("Connecting to RTSP server: " << host << ":" << port);

	// 创建TCP socket连接到RTSP服务器
	QTcpSocket socket;
	socket.connectToHost(host, port);

	// 等待连接建立或超时
	if (!socket.waitForConnected(timeoutMs))
	{
		LOG_DEBUG("RTSP connection error: " << socket.errorString());
		return false;
	}

	LOG_DEBUG("RTSP TCP connection established");

	// 构建RTSP OPTIONS请求
	QString path = url.path();
	if (path.isEmpty())
	{
		path = "/";
	}

	// 创建认证头（如果有）
	QString authHeader;
	// 手动解析rtspUrl获取用户名和密码，避免QUrl代理问题
	QString userPass = "";
	if (rtspUrl.contains("@"))
	{
		int protocolEnd = rtspUrl.indexOf("://") + 3;
		int authEnd = rtspUrl.indexOf("@", protocolEnd);
		if (authEnd > protocolEnd)
		{
			userPass = rtspUrl.mid(protocolEnd, authEnd - protocolEnd);
			// 直接使用从URL中提取的用户名和密码字符串
			authHeader = "Authorization: Basic " + userPass.toUtf8().toBase64() + "\r\n";
			LOG_DEBUG("Using authentication: " << userPass);
		}
	}

	// 发送OPTIONS请求
	QString request = QString(
						  "OPTIONS %1 RTSP/1.0\r\n"
						  "CSeq: 1\r\n"
						  "%2"
						  "User-Agent: LiveCamera RTSP Tester\r\n"
						  "\r\n")
						  .arg(path)
						  .arg(authHeader);

	LOG_DEBUG("Sending RTSP OPTIONS request");
	socket.write(request.toUtf8());

	// 等待响应或超时
	bool success = false;
	if (socket.waitForReadyRead(timeoutMs))
	{
		QByteArray response = socket.readAll();
		LOG_DEBUG("RTSP response received: " << response.size() << " bytes");

		// 检查是否收到了有效的RTSP响应
		if (response.contains("RTSP/1.0 200 OK") ||
			response.contains("RTSP/1.0 401 Unauthorized") || // 认证错误也表示服务器在线
			response.contains("RTSP/1.0 404 Not Found"))
		{ // 路径错误但服务器在线
			success = true;
		}
		else
		{
			LOG_DEBUG("Invalid RTSP response: " << response);
		}
	}
	else
	{
		LOG_DEBUG("No RTSP response received: " << socket.errorString());
	}

	// 关闭连接
	socket.disconnectFromHost();
	if (socket.state() != QAbstractSocket::UnconnectedState)
	{
		socket.waitForDisconnected(1000);
	}

	qint64 elapsedTime = timeoutTimer.elapsed();
	LOG_DEBUG("RTSP connection test result: " << (success ? "Success" : "Failed") << " Time: " << elapsedTime << "ms");

	return success;
}

// 从IP构建HTTP图像URL
QString MyThread::buildHttpImageUrl(const QString &ip, const QString &username, const QString &password)
{
	// 使用QUrl类来构建URL，以确保正确处理特殊字符
	QUrl url;
	url.setScheme("http");
	url.setHost(ip);
	url.setPath("/image.jpg");

	// 添加认证信息（如果有）
	if (!username.isEmpty() && !password.isEmpty())
	{
		url.setUserName(username);
		url.setPassword(password);
	}

	// 返回URL字符串
	return url.toString();
}

// 测试HTTP图像是否有效
bool MyThread::testHttpImage(const QString &ip, const QString &username, const QString &password, int timeoutMs)
{
	// LOG_DEBUG("111111111111111Testing HTTP image: " << ip << " username: " << username << " password: " << password);
	//  构建HTTP图像URL
	QString url = buildHttpImageUrl(ip, username, password);

	LOG_DEBUG("Testing HTTP image: " << url);

	// 使用严格超时控制
	QElapsedTimer timeoutTimer;
	timeoutTimer.start();

	// 检查URL是否有效
	QUrl qurl(url);
	if (!qurl.isValid())
	{
		LOG_DEBUG("HTTP image request error: Invalid URL format");
		return false;
	}

	// 创建网络请求管理器
	QScopedPointer<QNetworkAccessManager> manager(new QNetworkAccessManager());
	QNetworkRequest request;
	request.setUrl(qurl);

	// 添加基本认证头（另一种认证方式）
	if (!username.isEmpty() && !password.isEmpty())
	{
		// 直接使用原始用户名和密码，不进行URL编码
		QString credentials = username + ":" + password;
		QByteArray auth = "Basic " + credentials.toUtf8().toBase64();
		request.setRawHeader("Authorization", auth);
		LOG_DEBUG("Using HTTP basic auth with credentials: " << username << ":******");
	}

	// 设置较短的超时
	timeoutMs = qMin(timeoutMs, 2000); // 最多2秒

	// 发送请求并等待响应
	QEventLoop loop;
	QTimer timer;
	timer.setSingleShot(true);

	bool requestError = false;
	QString errorMessage;
	QByteArray imageData;

	// 连接定时器超时信号
	QObject::connect(&timer, &QTimer::timeout, [&]()
					 {
		LOG_DEBUG("HTTP image request timeout");
		requestError = true;
		errorMessage = "Request timeout";
		loop.quit(); });

	// 发送请求
	QNetworkReply *reply = manager->get(request);

	// 连接请求完成信号
	QObject::connect(reply, &QNetworkReply::finished, [&]()
					 {
		if (reply->error() == QNetworkReply::NoError) {
			// 获取图像数据
			imageData = reply->readAll();
			
			// 检查是否是有效的图像
			QImage image = QImage::fromData(imageData);
			if (image.isNull()) {
				requestError = true;
				errorMessage = "Invalid image data";
			}
		} else {
			requestError = true;
			errorMessage = reply->errorString();
		}
		
		loop.quit(); });

	// 启动超时计时器
	timer.start(timeoutMs);

	// 等待请求完成或超时
	loop.exec();

	// 释放资源
	reply->deleteLater();

	// 断开连接
	QObject::disconnect(reply, nullptr, nullptr, nullptr);

	// 检查是否成功获取图像
	bool success = !requestError && !imageData.isEmpty();

	if (requestError)
	{
		LOG_DEBUG("HTTP image request error: " << errorMessage);
	}
	else if (imageData.isEmpty())
	{
		LOG_DEBUG("HTTP image data is empty");
		success = false;
	}

	LOG_DEBUG("HTTP image test result: " << (success ? "Success" : "Failed") << " Time: " << timeoutTimer.elapsed() << "ms");

	return success;
}

void MyThread::handleSocketError()
{
	// 使用sender()获取触发信号的socket对象
	QTcpSocket *socket = qobject_cast<QTcpSocket *>(sender());
	if (socket)
	{
		LOG_DEBUG("Socket错误: " << socket->error() << " - " << socket->errorString());

		// 设置标志并发送信号
		isRunning = false;
		emit disconnectSlot();
	}
}

#ifdef USE_FFMPEG
// FFmpeg初始化方法
bool MyThread::initFFmpeg()
{
	LOG_DEBUG("初始化FFmpeg，RTSP URL: " << rtspUrl);

	try
	{
		// 初始化FFmpeg网络功能
		avformat_network_init();

		// 分配格式上下文
		formatContext = avformat_alloc_context();
		if (!formatContext)
		{
			LOG_ERROR("无法分配FFmpeg格式上下文");
			return false;
		}

		// 设置选项，如TCP传输、超时等
		AVDictionary *options = nullptr;
		av_dict_set(&options, "rtsp_transport", "tcp", 0); // 使用TCP传输RTSP
		// av_dict_set(&options, "rtsp_transport", "udp", 0); // 使用UDP传输RTSP
		av_dict_set(&options, "stimeout", "5000000", 0); // 5秒超时，单位是微秒
		// av_dict_set(&options, "buffer_size", "1024000", 0);	  // 缓冲区大小
		av_dict_set(&options, "buffer_size", "8192000", 0); // 缓冲区大小
		// av_dict_set(&options, "buffer_size", "8388608", 0);	  // 缓冲区大小
		av_dict_set(&options, "max_delay", "500000", 0);	  // 最大延迟
		av_dict_set(&options, "reconnect", "1", 0);			  // 断线重连
		av_dict_set(&options, "reconnect_streamed", "1", 0);  // 流媒体重连
		av_dict_set(&options, "reconnect_delay_max", "5", 0); // 最大重连延迟

		// 设置中断回调，用于处理超时
		formatContext->interrupt_callback.callback = [](void *ctx) -> int
		{
			// 超时中断处理
			return 0; // 返回1表示中断
		};
		formatContext->interrupt_callback.opaque = this;

		// 打开输入流
		int ret = avformat_open_input(&formatContext, rtspUrl.toUtf8().constData(), nullptr, &options);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开RTSP流: " << rtspUrl << ", 错误: " << errBuf);
			return false;
		}

		// 查找流信息
		ret = avformat_find_stream_info(formatContext, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法找到流信息, 错误: " << errBuf);
			return false;
		}

		// 查找视频流
		videoStreamIndex = -1;
		for (unsigned int i = 0; i < formatContext->nb_streams; i++)
		{
			if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
			{
				videoStreamIndex = i;
				break;
			}
		}

		if (videoStreamIndex == -1)
		{
			LOG_ERROR("未找到视频流");
			return false;
		}

		// 获取编解码器
		AVCodecParameters *codecParams = formatContext->streams[videoStreamIndex]->codecpar;
		const AVCodec *codec = avcodec_find_decoder(codecParams->codec_id);
		if (!codec)
		{
			LOG_ERROR("未找到解码器");
			return false;
		}

		// 创建编解码器上下文
		codecContext = avcodec_alloc_context3(codec);
		if (!codecContext)
		{
			LOG_ERROR("无法分配编解码器上下文");
			return false;
		}

		// 将编解码器参数复制到上下文
		ret = avcodec_parameters_to_context(codecContext, codecParams);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法复制编解码器参数, 错误: " << errBuf);
			return false;
		}

		// 打开解码器
		ret = avcodec_open2(codecContext, codec, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开解码器, 错误: " << errBuf);
			return false;
		}

		// 分配帧和数据包
		frame = av_frame_alloc();
		if (!frame)
		{
			LOG_ERROR("无法分配帧");
			return false;
		}

		packet = av_packet_alloc();
		if (!packet)
		{
			LOG_ERROR("无法分配数据包");
			return false;
		}

		// 初始化成功
		LOG_DEBUG("FFmpeg初始化成功, 视频流索引: " << videoStreamIndex << ", 分辨率: " << codecContext->width << "x" << codecContext->height << ", 编解码器: " << codec->name);

		return true;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg初始化过程中发生异常: " << e.what());
		return false;
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg初始化过程中发生未知异常");
		return false;
	}
}

// FFmpeg清理方法
void MyThread::cleanupFFmpeg()
{
	try
	{
		LOG_DEBUG("清理FFmpeg资源");

		// 清理音频资源
		cleanupAudioOutput();

		if (swsContext)
		{
			sws_freeContext(swsContext);
			swsContext = nullptr;
		}

		if (frame)
		{
			av_frame_free(&frame);
			frame = nullptr;
		}

		if (packet)
		{
			av_packet_free(&packet);
			packet = nullptr;
		}

		if (codecContext)
		{
			avcodec_free_context(&codecContext);
			codecContext = nullptr;
		}

		if (formatContext)
		{
			avformat_close_input(&formatContext);
			formatContext = nullptr;
		}

		LOG_DEBUG("FFmpeg资源清理完成");
	}
	catch (...)
	{
		LOG_ERROR("清理FFmpeg资源时发生未知异常");
	}
}

// 使用FFmpeg处理RTSP流
void MyThread::handleRtspWithFFmpeg()
{
	LOG_DEBUG("使用FFmpeg处理RTSP流: " << rtspUrl);

	try
	{
		// 初始化FFmpeg
		if (!initFFmpeg())
		{
			LOG_ERROR("FFmpeg初始化失败");

			isRunning = false;
			m_threadStopped = true;

			emit disconnectSlot();
			return;
		}

		// 开始读取和处理帧
		LOG_DEBUG("开始读取视频帧");

		// 获取视频流的帧率
		double framerate = 25.0; // 默认帧率
		if (formatContext->streams[videoStreamIndex]->avg_frame_rate.num &&
			formatContext->streams[videoStreamIndex]->avg_frame_rate.den)
		{
			framerate = (double)formatContext->streams[videoStreamIndex]->avg_frame_rate.num /
						formatContext->streams[videoStreamIndex]->avg_frame_rate.den;
		}
		int frameInterval = 1000 / (int)framerate; // 帧间隔(毫秒)
		LOG_DEBUG("视频帧率: " << framerate << " fps, 帧间隔: " << frameInterval << " ms");

		// 帧缓存控制 - 参考src中的DEFAULT_FRAME_CACHE
		const int MAX_FRAME_CACHE = 10;
		int queuedFrames = 0;

		// 用于计算帧率
		int frameCount = 0;
		QTime fpsTimer;
		fpsTimer.start();

		// 用于周期性检查线程状态
		QElapsedTimer checkTimer;
		checkTimer.start();

		// 用于帧同步
		QElapsedTimer frameTimer;
		frameTimer.start();

		// 用于检测和处理延迟
		QElapsedTimer delayTimer;
		delayTimer.start();
		int64_t lastPts = AV_NOPTS_VALUE;
		int skipFrameCount = 0;
		int processedFrameCount = 0;
		bool frameDelayDetected = false;

		// 预先准备QImage对象，避免频繁创建和销毁
		QImage processedImage;
		bool hasInitializedImage = false;

		while (true)
		{
			try
			{
				// 周期性检查线程是否应该停止（每100毫秒检查一次）
				if (checkTimer.elapsed() > 100)
				{
					if (!isRunning)
					{
						break;
					}
					checkTimer.restart();
				}

				// 每10秒检查一次是否出现延迟问题
				if (delayTimer.elapsed() > 10000)
				{
					LOG_DEBUG("性能统计: 处理帧数=" << processedFrameCount << ", 丢弃帧数=" << skipFrameCount
													<< ", 当前队列=" << queuedFrames);
					processedFrameCount = 0;
					skipFrameCount = 0;
					delayTimer.restart();
				}

				// 读取下一帧
				int ret = av_read_frame(formatContext, packet);
				if (ret < 0)
				{
					// 遇到错误，可能是流结束或网络问题
					if (ret == AVERROR_EOF || ret == AVERROR(EAGAIN))
					{
						// 流结束或需要等待更多数据
						LOG_DEBUG("等待更多数据或流结束");
						QThread::msleep(100);
						continue;
					}
					else
					{
						// 其他错误
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("读取帧失败: " << errBuf);

						// 重试几次后再退出
						static int errorCount = 0;
						if (++errorCount > 5)
						{
							LOG_ERROR("连续读取帧失败超过5次，退出处理");
							break;
						}

						QThread::msleep(200);
						continue;
					}
				}

				// 处理视频帧
				if (packet->stream_index == videoStreamIndex)
				{
					// 缓存控制 - 如果队列中的帧过多，使用上一帧而不是丢弃非关键帧
					if (queuedFrames >= MAX_FRAME_CACHE)
					{
						// 当缓存满时，不处理新帧，但也不丢弃，直接使用上一帧
						skipFrameCount++;
						av_packet_unref(packet);

						// 如果有有效的上一帧，则重新发送该帧
						if (hasValidLastFrame && isRunning)
						{
							// 使用互斥锁保护lastFrame的访问
							mutex.lock();
							QImage copyToSend = lastCompleteFrame.copy(); // 创建副本用于发送
							mutex.unlock();

							// 发送上一帧
							emit transmitData(copyToSend);
							LOG_DEBUG_ONCE("缓冲区满，重用上一帧");
						}

						continue; // 跳过当前帧的处理
					}

					// 时间戳检查，防止延迟累积
					if (packet->pts != AV_NOPTS_VALUE && lastPts != AV_NOPTS_VALUE)
					{
						// 获取时间基准
						AVRational time_base = formatContext->streams[videoStreamIndex]->time_base;

						// 计算相对于上一帧的时间戳差异（毫秒）
						int64_t pts_diff = av_rescale_q(packet->pts - lastPts,
														time_base,
														AVRational{1, 1000});

						// 计算预期的帧间隔时间（毫秒）
						int expected_interval = frameInterval;

						// 如果时间戳差异超过预期间隔的5倍，检测到严重延迟
						if (pts_diff > expected_interval * 5 && !frameDelayDetected)
						{
							LOG_WARNING("检测到视频延迟: 时间戳差异=" << pts_diff << "ms, 预期间隔=" << expected_interval << "ms");
							frameDelayDetected = true;

							// 遇到严重延迟时，清空队列
							queuedFrames = 0;
						}
					}

					// 更新最后一个时间戳
					if (packet->pts != AV_NOPTS_VALUE)
					{
						lastPts = packet->pts;
					}

					// 增加队列中的帧数
					queuedFrames++;

					// 发送数据包到解码器 - 不需要锁
					ret = avcodec_send_packet(codecContext, packet);
					if (ret < 0)
					{
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("发送视频数据包失败: " << errBuf);
						av_packet_unref(packet);
						queuedFrames--; // 调整队列计数
						continue;
					}

					// 从解码器接收帧
					bool frameProcessed = false;
					while (ret >= 0 && !frameProcessed)
					{
						// 再次检查线程状态
						if (!isRunning)
						{
							break;
						}

						ret = avcodec_receive_frame(codecContext, frame);
						if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
						{
							// 需要更多输入数据或已到流尾
							break;
						}
						else if (ret < 0)
						{
							char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
							av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
							LOG_ERROR("接收视频帧失败: " << errBuf);
							break;
						}

						try
						{
							// 创建或更新缩放上下文
							if (!swsContext)
							{
								swsContext = sws_getContext(
									codecContext->width, codecContext->height, codecContext->pix_fmt,
									codecContext->width, codecContext->height, AV_PIX_FMT_RGB24,
									SWS_BICUBIC, nullptr, nullptr, nullptr); // 使用SWS_BICUBIC替代SWS_BILINEAR

								if (!swsContext)
								{
									LOG_ERROR("无法创建缩放上下文");
									break;
								}
							}

							// 创建QImage接收转换后的数据 - 重用已有图像对象
							if (!hasInitializedImage || processedImage.width() != codecContext->width ||
								processedImage.height() != codecContext->height)
							{
								processedImage = QImage(codecContext->width, codecContext->height, QImage::Format_RGB888);
								hasInitializedImage = true;
							}

							// 设置目标数据指针和行大小
							uint8_t *destData[4] = {processedImage.bits(), nullptr, nullptr, nullptr};
							int destLinesize[4] = {processedImage.bytesPerLine(), 0, 0, 0};

							// 执行颜色空间转换 - 这个操作不需要锁
							sws_scale(swsContext, frame->data, frame->linesize, 0,
									  codecContext->height, destData, destLinesize);

							// 基于帧率控制帧发送节奏
							int elapsed = frameTimer.elapsed();
							if (elapsed < frameInterval && !frameDelayDetected)
							{
								// 等待适当的时间以保持正确的帧率
								QThread::msleep(frameInterval - elapsed);
							}
							frameTimer.restart();

							// 在这里进行传输前的缓冲区处理
							QImage copyToSend = processedImage.copy(); // 创建副本用于发送

							// 验证帧质量，确保只保存高质量的帧作为lastCompleteFrame
							bool isGoodQualityFrame = true;

							// 1. 检查是否为关键帧 - 关键帧通常质量更好
							bool isKeyFrame = (frame->key_frame == 1) || (frame->pict_type == AV_PICTURE_TYPE_I);

							// 2. 检查图像是否包含有效数据 - 避免全黑或全白的帧
							if (!copyToSend.isNull() && copyToSend.width() > 0 && copyToSend.height() > 0)
							{
								// 采样检查图像质量 - 检查部分像素点的变化
								// 这里简单实现，实际应用可能需要更复杂的图像质量评估算法
								static QRgb lastPixelSample = 0;
								QRgb currentPixel = copyToSend.pixel(copyToSend.width() / 2, copyToSend.height() / 2);

								// 如果图像中心像素与上次相同，可能是静止帧或损坏帧
								static int samePixelCount = 0;
								if (lastPixelSample == currentPixel && !isKeyFrame)
								{
									if (++samePixelCount > 5)
									{ // 连续5帧相同像素点视为可疑
										LOG_DEBUG_ONCE("检测到可疑的静止帧或损坏帧");
										isGoodQualityFrame = false;
									}
								}
								else
								{
									lastPixelSample = currentPixel;
									samePixelCount = 0; // 重置计数器
								}

								// 3. 增强的灰白色调检测 - 使用直方图分析
								// 计算图像的颜色分布
								int grayPixels = 0;
								int totalPixels = 0;
								int samplingStep = 10; // 每10个像素采样一次，提高性能

								for (int y = 0; y < copyToSend.height(); y += samplingStep)
								{
									for (int x = 0; x < copyToSend.width(); x += samplingStep)
									{
										QRgb pixel = copyToSend.pixel(x, y);
										int r = qRed(pixel);
										int g = qGreen(pixel);
										int b = qBlue(pixel);

										// 检查是否为灰白色调 - 更严格的条件
										if (std::abs(r - g) < 12 && std::abs(g - b) < 12 && std::abs(r - b) < 12)
										{
											// 灰度像素
											if (r > 180 && g > 180 && b > 180)
											{
												// 偏白灰色
												grayPixels++;
											}
										}
										totalPixels++;
									}
								}

								// 计算灰白像素的比例
								double grayRatio = (double)grayPixels / totalPixels;

								// 如果灰白像素比例超过阈值，标记为低质量帧
								if (grayRatio > 0.6)
								{ // 60%以上像素是灰白色
									LOG_DEBUG_ONCE("检测到高比例灰白色调帧: " << (grayRatio * 100) << "%");
									isGoodQualityFrame = false;
									consecutiveGrayFrames++;

									// 如果连续多帧都是灰白色调，可能是摄像头本身问题，记录日志
									if (consecutiveGrayFrames > 10)
									{
										LOG_WARNING("连续检测到10帧以上灰白色调，可能是摄像头设置问题");
										// 重置计数器，避免日志刷屏
										consecutiveGrayFrames = 0;
									}
								}
								else
								{
									consecutiveGrayFrames = 0; // 重置计数器
								}

								// 4. 饱和度检测 - 计算平均饱和度
								double totalSaturation = 0.0;
								for (int y = 0; y < copyToSend.height(); y += samplingStep)
								{
									for (int x = 0; x < copyToSend.width(); x += samplingStep)
									{
										QRgb pixel = copyToSend.pixel(x, y);
										int r = qRed(pixel);
										int g = qGreen(pixel);
										int b = qBlue(pixel);

										// 计算RGB最大最小值
										int maxVal = std::max(r, std::max(g, b));
										int minVal = std::min(r, std::min(g, b));

										// 计算饱和度 (0-1范围)
										double saturation = (maxVal > 0) ? ((double)(maxVal - minVal) / maxVal) : 0;
										totalSaturation += saturation;
									}
								}

								double avgSaturation = totalSaturation / totalPixels;

								// 如果平均饱和度过低，可能是灰白色调
								if (avgSaturation < 0.15)
								{ // 饱和度阈值
									LOG_DEBUG_ONCE("检测到低饱和度帧: " << avgSaturation);
									isGoodQualityFrame = false;
								}
							}
							else
							{
								isGoodQualityFrame = false;
							}

							// 更新lastFrame - 使用最小的互斥锁范围
							mutex.lock();
							lastFrame = copyToSend; // 深拷贝，避免数据竞争

							// 备用帧管理 - 保存最近的5个高质量帧
							static const int MAX_BACKUP_FRAMES = 5;
							static QImage backupFrames[MAX_BACKUP_FRAMES];
							static bool hasBackupFrames[MAX_BACKUP_FRAMES] = {false};

							// 只有质量良好的帧才保存为lastCompleteFrame
							if (isGoodQualityFrame)
							{
								// 保存这一帧作为最后的完整帧
								lastCompleteFrame = copyToSend;
								hasValidLastFrame = true;
								LOG_DEBUG_ONCE("保存高质量帧作为备用");

								// 同时保存到备用帧数组
								backupFrames[backupFrameIndex] = copyToSend;
								hasBackupFrames[backupFrameIndex] = true;
								backupFrameIndex = (backupFrameIndex + 1) % MAX_BACKUP_FRAMES;
							}
							else if (!hasValidLastFrame)
							{
								// 如果当前没有有效的lastCompleteFrame，但有备用帧，使用最近的备用帧
								for (int i = 0; i < MAX_BACKUP_FRAMES; i++)
								{
									if (hasBackupFrames[i])
									{
										lastCompleteFrame = backupFrames[i];
										hasValidLastFrame = true;
										LOG_DEBUG_ONCE("使用备用帧替代低质量帧");
										break;
									}
								}
							}
							mutex.unlock();

							// 再次检查线程状态
							if (isRunning)
							{
								emit transmitData(copyToSend);
								processedFrameCount++;
								frameProcessed = true;

								// 成功处理一帧后，减少队列计数
								queuedFrames--;
							}

							// 计算并显示帧率
							frameCount++;
							if (fpsTimer.elapsed() >= 5000)
							{ // 每5秒显示一次帧率
								double fps = frameCount / (fpsTimer.elapsed() / 1000.0);
								LOG_DEBUG("当前帧率: " << fps << " fps, 队列中帧数: " << queuedFrames);
								frameCount = 0;
								fpsTimer.restart();
							}
						}
						catch (const std::exception &e)
						{
							LOG_ERROR("处理视频帧数据时发生异常: " << e.what());
						}
						catch (...)
						{
							LOG_ERROR("处理视频帧数据时发生未知异常");
						}
					}
				}

				// 释放数据包
				av_packet_unref(packet);

				// 防止CPU过载，动态调整睡眠时间
				if (frameDelayDetected)
				{
					// 如果检测到延迟，不睡眠或睡眠很短时间
					QThread::yieldCurrentThread(); // 让出时间片给其他线程
				}
				else if (queuedFrames < MAX_FRAME_CACHE / 2)
				{
					// 如果队列不满，适当增加睡眠时间以节省CPU
					QThread::msleep(5);
				}
				else
				{
					// 正常情况下的睡眠时间
					QThread::msleep(1);
				}
			}
			catch (const std::exception &e)
			{
				LOG_ERROR("处理帧时发生异常: " << e.what());
				QThread::msleep(500); // 出错后稍微暂停一下
			}
			catch (...)
			{
				LOG_ERROR("处理帧时发生未知异常");
				QThread::msleep(500);
			}
		}

		// 清理资源 - 使用互斥锁保护
		mutex.lock();
		cleanupFFmpeg();
		mutex.unlock();

		LOG_DEBUG("FFmpeg处理结束");
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg处理过程中发生异常: " << e.what());

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg处理过程中发生未知异常");

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
}

// 初始化音频输出
bool MyThread::initAudioOutput(int sampleRate, int channels, int sampleFormat)
{
	if (audioInitialized)
	{
		// 已经初始化过，直接返回
		return true;
	}

	LOG_DEBUG("初始化音频输出: 采样率=" << sampleRate << ", 声道数=" << channels << ", 格式=" << sampleFormat);

	try
	{
		// 设置音频输出参数
		QAudioFormat format;
		format.setSampleRate(sampleRate);
		format.setChannelCount(channels);
		format.setSampleSize(16); // 16位PCM
		format.setCodec("audio/pcm");
		format.setByteOrder(QAudioFormat::LittleEndian);
		format.setSampleType(QAudioFormat::SignedInt);

		// 检查格式是否支持
		QAudioDeviceInfo info(QAudioDeviceInfo::defaultOutputDevice());
		if (!info.isFormatSupported(format))
		{
			LOG_WARNING("默认音频设备不支持请求的格式，使用最接近的格式");
			format = info.nearestFormat(format);
			LOG_DEBUG("已调整: 采样率=" << format.sampleRate() << ", 声道数=" << format.channelCount()
										<< ", 样本大小=" << format.sampleSize());
		}

		// 创建音频输出
		audioOutput = new QAudioOutput(format);
		if (!audioOutput)
		{
			LOG_ERROR("无法创建QAudioOutput");
			return false;
		}

		// 设置缓冲区大小为0.5秒
		int bufferSize = (format.sampleRate() * format.channelCount() * format.sampleSize() / 8) / 2;
		audioOutput->setBufferSize(bufferSize);

		// 创建缓冲区和设备
		audioBuffer.clear();
		audioBufferDevice = new QBuffer(&audioBuffer);
		audioBufferDevice->open(QIODevice::ReadWrite);

		// 打开音频设备
		audioDevice = audioOutput->start();
		if (!audioDevice)
		{
			LOG_ERROR("无法启动音频输出");
			delete audioOutput;
			audioOutput = nullptr;
			delete audioBufferDevice;
			audioBufferDevice = nullptr;
			return false;
		}

		// 创建重采样上下文
		if (swrContext)
		{
			swr_free(&swrContext);
		}

		// 确定输入和输出格式
		AVSampleFormat inFormat = (AVSampleFormat)sampleFormat;
		AVSampleFormat outFormat = AV_SAMPLE_FMT_S16; // 16位有符号整数

		// 创建音频重采样上下文
		swrContext = swr_alloc_set_opts(
			nullptr,
			av_get_default_channel_layout(channels), // 输出声道布局
			outFormat,								 // 输出格式
			sampleRate,								 // 输出采样率
			av_get_default_channel_layout(channels), // 输入声道布局
			inFormat,								 // 输入格式
			sampleRate,								 // 输入采样率
			0,										 // 日志偏移
			nullptr									 // 日志上下文
		);

		if (!swrContext)
		{
			LOG_ERROR("无法创建音频重采样上下文");
			return false;
		}

		// 初始化重采样上下文
		if (swr_init(swrContext) < 0)
		{
			LOG_ERROR("无法初始化音频重采样上下文");
			swr_free(&swrContext);
			swrContext = nullptr;
			return false;
		}

		// 保存参数
		audioOutSampleRate = sampleRate;
		audioOutChannels = channels;
		audioInitialized = true;

		LOG_DEBUG("音频输出初始化成功");
		return true;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("音频初始化异常: " << e.what());
		return false;
	}
	catch (...)
	{
		LOG_ERROR("音频初始化未知异常");
		return false;
	}
}

// 清理音频输出资源
void MyThread::cleanupAudioOutput()
{
	LOG_DEBUG("清理音频输出资源");

	if (audioOutput)
	{
		audioOutput->stop();
		delete audioOutput;
		audioOutput = nullptr;
	}

	if (audioBufferDevice)
	{
		audioBufferDevice->close();
		delete audioBufferDevice;
		audioBufferDevice = nullptr;
	}

	audioBuffer.clear();
	audioDevice = nullptr;

	if (swrContext)
	{
		swr_free(&swrContext);
		swrContext = nullptr;
	}

	audioInitialized = false;
}

// 处理解码后的音频帧
void MyThread::processAudioFrame(AVFrame *frame)
{
	if (!frame || isMuted || !audioInitialized || !audioDevice)
	{
		return;
	}

	try
	{
		// 计算输出样本数量
		int outSamples = av_rescale_rnd(
			swr_get_delay(swrContext, frame->sample_rate) + frame->nb_samples,
			audioOutSampleRate,
			frame->sample_rate,
			AV_ROUND_UP);

		// 分配输出缓冲区
		uint8_t *outBuffer;
		int outLinesize;
		if (av_samples_alloc(&outBuffer, &outLinesize, audioOutChannels, outSamples, AV_SAMPLE_FMT_S16, 0) < 0)
		{
			LOG_ERROR("无法分配音频输出缓冲区");
			return;
		}

		// 执行重采样
		int samplesOut = swr_convert(
			swrContext,
			&outBuffer, outSamples,
			(const uint8_t **)frame->data, frame->nb_samples);

		if (samplesOut > 0)
		{
			// 计算数据大小（每个样本2字节，16位PCM）
			int dataSize = samplesOut * audioOutChannels * 2;

			// 清空缓冲区
			audioBufferDevice->seek(0);
			audioBuffer.clear();

			// 将数据写入缓冲区
			audioBufferDevice->write((const char *)outBuffer, dataSize);
			audioBufferDevice->seek(0);

			// 将数据写入音频设备
			if (audioDevice->write(audioBuffer) < 0)
			{
				LOG_ERROR("写入音频设备失败");
			}
		}

		// 释放输出缓冲区
		av_freep(&outBuffer);
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("处理音频帧异常: " << e.what());
	}
	catch (...)
	{
		LOG_ERROR("处理音频帧未知异常");
	}
}
#endif

// 新增：使用外部ffmpeg.exe进行录像的方法
bool MyThread::startRecordingWithFFmpegExe(const QString &outputFile)
{
	if (!isRunning)
	{
		LOG_ERROR("线程未运行，无法开始录像");
		return false;
	}

	// 检查ffmpeg.exe是否存在
	QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
	if (!QFile::exists(ffmpegPath))
	{
		LOG_ERROR("未找到ffmpeg.exe: " << ffmpegPath);
		return false;
	}

	// 停止之前的录像进程（如果有）
	stopRecordingWithFFmpegExe();

	// 创建录像进程
	recordProcess = new QProcess(this);

	// 连接进程结束信号，以便清理资源
	connect(recordProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
			[this](int exitCode, QProcess::ExitStatus exitStatus)
			{
				LOG_DEBUG("录像进程结束，退出码: " << exitCode);
				recordProcess->deleteLater();
				recordProcess = nullptr;
			});

	// 连接错误信号
	connect(recordProcess, &QProcess::errorOccurred,
			[this](QProcess::ProcessError error)
			{
				LOG_ERROR("录像进程错误: " << error);
			});

	// 设置命令行参数
	QStringList arguments;

	// 输入是RTSP流
	if (isRtspMode)
	{
		arguments << "-i" << rtspUrl;
	}
	else
	{
		// 对于非RTSP模式，可能需要其他输入方式，根据实际情况调整
		LOG_ERROR("非RTSP模式暂不支持录像");
		return false;
	}

	// 添加编码和输出选项
	arguments << "-c:v" << "copy" // 复制视频流，不重新编码
			  << "-c:a" << "copy" // 复制音频流，不重新编码
			  << "-y"			  // 覆盖已有文件
			  << outputFile;	  // 输出文件路径

	LOG_DEBUG("启动录像进程: " << ffmpegPath << arguments.join(" "));

	// 启动进程
	recordProcess->start(ffmpegPath, arguments);

	// 等待进程启动
	if (!recordProcess->waitForStarted(3000))
	{
		LOG_ERROR("录像进程启动失败");
		recordProcess->deleteLater();
		recordProcess = nullptr;
		return false;
	}

	LOG_INFO("录像已开始，输出到: " << outputFile);
	return true;
}

// 新增：停止外部ffmpeg.exe录像
void MyThread::stopRecordingWithFFmpegExe()
{
	if (recordProcess)
	{
// 发送退出信号
#ifdef Q_OS_WIN
		// Windows下使用taskkill强制结束进程
		QProcess::execute("taskkill", QStringList() << "/F" << "/PID" << QString::number(recordProcess->processId()));
#else
		// 其他系统使用terminate
		recordProcess->terminate();
		if (!recordProcess->waitForFinished(3000))
		{
			recordProcess->kill();
		}
#endif

		recordProcess->deleteLater();
		recordProcess = nullptr;
		LOG_INFO("录像已停止");
	}
}
